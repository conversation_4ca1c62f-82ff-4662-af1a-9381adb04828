import Foundation
import SwiftUI

// MARK: - 聊天设置管理器

@MainActor
final class ChatSettingsManager: ObservableObject {
    @Published var enableThinking = false
    @Published var enableNetworking = false

    func toggleThinking() {
        enableThinking.toggle()

        if enableThinking, enableNetworking {
            enableNetworking = false
        }
    }

    func toggleNetworking() {
        enableNetworking.toggle()

        if enableNetworking, enableThinking {
            enableThinking = false
        }
    }
}
