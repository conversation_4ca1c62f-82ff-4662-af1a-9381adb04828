//
//  ImageRecognitionChatController.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/8.
//

import SwiftUI
import Combine


// MARK: - 图片识别聊天控制器

enum ChatMode_graphId:String {
    case imageRecognition = "vision_graph"
}

@MainActor
class ImageRecognitionChatController: ObservableObject {
    
    // MARK: - 服务的依赖注入
    private let chatService: ChatService
    private let fileService: FileService
    private let threadManager = ThreadManager.shared
    
    // MARK: - 任务管理
    private var currentStreamTask: Task<Void, Never>?
    private var uploadTask: Task<Void, Never>?
    
    // MARK: - 聊天核心状态
    @Published var messages: [ChatMessageModel] = []
    @Published var inputText = ""
    @Published var isLoading = false
    var uploadLoading = false
    @Published var loadingType: ChatLoadingType = .none
    @Published var errorMessage = ""
    @Published var showError = false
    
    
    // MARK: - 图片识别专用状态
    
    /// 已上传并准备好随下一条消息发送的图片文件信息。
    //    @Published var stagedImageFiles: [UploadImagesRes] = []
    
    /// 用户新选择的、尚未上传的图片
    @Published var newPickedImages: [UIImage] = []
    var handlePickedIamges: [(UIImage, String)] = []
    
    /// 当前选择的识别类型
    @Published var selectedVisionType: VisionType = .vision
    
    /// 已上传并用于当前对话的图片文件信息 (用于构建消息体)
    @Published var uploadedImageFiles: [UploadImagesRes] = []
    
    /// 一个信号，通知UI在消息发送后清空已上传的图片预览
    @Published var shouldClearUploadedImages = false
    

    let chatMode_graphId: ChatMode_graphId = .imageRecognition
    
    // MARK: - 流式和重试状态
    private var currentAIMessage: ChatMessageModel?
    private var isRetryingMessage = false
    private var retryingMessageId: UUID?
    
    // MARK: - 初始化和销毁
    
    init(
        chatService: ChatService = ChatService(),
        fileService: FileService = FileService()
    ) {
        self.chatService = chatService
        self.fileService = fileService
    }
    
    /// 当 Controller 被销毁时，取消所有正在进行的任务
    /// 停止所有正在进行的任务。
    deinit {
        uploadTask?.cancel()
        currentStreamTask?.cancel()
        
        uploadTask = nil
        currentStreamTask = nil
    }

    
    
   
    // MARK: - 公共方法
    
    /// 分享消息内容
    func shareMessage(_ message: ChatMessageModel) {
        let shareContent = MessageActionBar.formatMessageForSharing(message)

        // 使用系统分享功能
        guard let windowScene = UIApplication.shared.connectedScenes
            .compactMap({ $0 as? UIWindowScene })
            .first,
            let rootViewController = windowScene.windows.first?.rootViewController
        else {
            print("❌ ChatController: 无法获取根视图控制器")
            return
        }

        let activityController = UIActivityViewController(
            activityItems: [shareContent],
            applicationActivities: nil
        )

        // iPad 支持
        if let popover = activityController.popoverPresentationController {
            popover.sourceView = rootViewController.view
            popover.sourceRect = CGRect(
                x: rootViewController.view.bounds.midX,
                y: rootViewController.view.bounds.midY,
                width: 0,
                height: 0
            )
            popover.permittedArrowDirections = []
        }

        rootViewController.present(activityController, animated: true)
    }
    
    /// 重置当前会话线程
    func resetCurrentThread(clearSession: Bool = true) {
        threadManager.clearCurrentSession()
    }
    
    
    /// 复制消息内容
    /// - Parameter message: 要复制的消息
    func copyMessage(_ message: ChatMessageModel) {
        let content = MessageActionBar.extractMainContent(from: message)
        UIPasteboard.general.string = content

        showCopySuccessToast()
    }
    
    //重新生成
    func retryMessage(messageId: UUID) {
            // 1. 检查是否已在加载中，防止重复触发
            guard !isLoading else {
                print("ChatController: 正在处理另一项请求，请稍后重试。")
                return
            }

            print("ImageRecognitionChatController: 准备重试消息 \(messageId)")

            // 2. 在消息列表中找到要重试的AI消息
            guard let messageIndex = messages.firstIndex(where: { $0.id == messageId && !$0.isUser }) else {
                print("❌ ImageRecognitionChatController: 未在消息列表中找到要重试的AI消息 (ID: \(messageId))。")
                return
            }

            // 3. 从AI消息的位置向前搜索，找到对应的用户消息
            var correspondingUserMessage: ChatMessageModel?
            for i in (0..<messageIndex).reversed() {
                if messages[i].isUser {
                    correspondingUserMessage = messages[i]
                    break // 找到最近的一个用户消息后立即停止
                }
            }

            guard let userMessage = correspondingUserMessage else {
                print("❌ ImageRecognitionChatController: 未能找到与要重试的AI消息对应的用户消息。")
                return
            }

            print("将针对用户消息进行重试。文本: '\(userMessage.content)', 图片数: \(userMessage.imageAttachments.count)")

            // 4. 从找到的用户消息中提取用于重试的文本和图片信息
            let messageTextToRetry = userMessage.content
            
            // 将 MessageImage 结构转换回请求所需的 UploadImagesRes 结构
            let filesToRetry = userMessage.imageAttachments.map { imageAttachment in
                UploadImagesRes(
                    id: Int(imageAttachment.fileId) ?? 0,
                    fileId: imageAttachment.fileId,
                    name: imageAttachment.fileName,
                    originalName: imageAttachment.fileName,
                    url: imageAttachment.fileUrl
                )
            }

            // 5. 设置加载和重试状态，为新的流式请求做准备
            isLoading = true
            loadingType = .streaming
            isRetryingMessage = true
            retryingMessageId = messageId // 关键：记录要替换的消息ID，ensureCurrentAIMessage会使用它
            errorMessage = ""
            showError = false

            // 6. 启动一个异步任务来发送重试请求
            Task {
                await sendImageRecognitionRequest(
                    message: messageTextToRetry,
                    stagedFiles: filesToRetry,
                    isRetry: true, // 标记为重试请求
                    isFirst: false
                )
            }
        }
    
    //MARK: - regenerateLastMessage
    /// 重新生成最后一条AI消息。
    func regenerateLastMessage() {
        guard !isLoading else { return }
        
        guard let lastAIMessageIndex = messages.lastIndex(where: { !$0.isUser }) else {
            print("❌ 找不到可以重新生成的AI消息。")
            return
        }
        
        let lastAIMessage = messages[lastAIMessageIndex]
        
        // 找到对应的用户消息
        var correspondingUserMessage: ChatMessageModel?
        for i in (0..<lastAIMessageIndex).reversed() {
            if messages[i].isUser {
                correspondingUserMessage = messages[i]
                break
            }
        }
        
        guard let userMessage = correspondingUserMessage else {
            print("❌ 找不到与最后一条AI消息对应的用户消息。")
            return
        }
        
        print("准备重新生成针对用户消息的回答。文本: '\(userMessage.content)', 图片数: \(userMessage.imageAttachments.count)")
        
        // 提取图片和文本信息用于重试
        let filesToRetry = userMessage.imageAttachments.map {
            UploadImagesRes(
                id: Int($0.fileId) ?? 0,
                fileId: $0.fileId,
                name: $0.fileName,
                originalName: $0.fileName,
                url: $0.fileUrl
            )
        }
        let messageToRetry = userMessage.content
        
        // 设置加载和重试状态
        isLoading = true
        loadingType = .streaming
        isRetryingMessage = true
        retryingMessageId = lastAIMessage.id
        errorMessage = ""
        showError = false
        
        // 发送重试请求
        Task {
            await sendImageRecognitionRequest(
                message: messageToRetry,  // 使用原始消息的文本
                stagedFiles: filesToRetry, // 使用原始消息的图片
                isRetry: true,
                isFirst: false
            )
        }
    }
    
    /// 加载历史对话
    func loadHistoryChat(threadId: String) {
        print("ChatController: 开始加载历史对话 - threadId: \(threadId), chatMode: \(chatMode_graphId)")

        stopStreaming()

        currentAIMessage = nil
        isRetryingMessage = false
        retryingMessageId = nil

        messages.removeAll()
        errorMessage = ""
        showError = false

        isLoading = true
        loadingType = .loadingHistory

        Task {
            await performLoadHistoryChat(threadId: threadId)
        }
    }
    
    
    
  
    //MARK: - stopStreaming
    /// 停止当前流式响应。
    /// 这会取消正在进行的网络任务，并重置相关的加载状态。
    func stopStreaming() {
        guard currentStreamTask != nil || isLoading else {
            return
        }
        
        print("ChatController: 停止流式响应")
        
        currentStreamTask?.cancel()
        uploadTask?.cancel()
        
        currentStreamTask = nil
        uploadTask = nil
        
        isLoading = false
        loadingType = .none
        
        // 尝试断开SSE连接
        Task {
            await EventSourceAdapter.shared.disconnect()
            
            let isDisconnected = EventSourceAdapter.shared.isFullyDisconnected
            
            if !isDisconnected {
                print("ChatController: SSE连接可能未完全断开")
                print(EventSourceAdapter.shared.debugConnectionInfo)
            } else {
                print("ChatController: SSE连接已完全断开")
            }
        }
    }

    func claerPickedImages (){
        print("清除以选中的照片")
        newPickedImages.removeAll()
        handlePickedIamges.removeAll()
    }
    //MARK: - clearAllState
    /// 清理整个聊天界面和状态。
    func clearAllState() {
        stopStreaming()
        messages.removeAll()
        inputText = ""
        uploadedImageFiles.removeAll()
        threadManager.clearCurrentSession()
        resetLoadingState()
        claerPickedImages()
        
        errorMessage = ""
        showError = false
        currentAIMessage = nil
        isRetryingMessage = false
        retryingMessageId = nil
        print("所有状态已清理。")
    }
    
    // MARK: - 私有核心逻辑
    
    
    
    
    //MARK: uploadInitialImages
    /// 在初始化时自动调用的上传方法。
    func uploadInitialImages() {
        guard uploadTask == nil else { return }
        guard !newPickedImages.isEmpty else {return}
        
        isLoading = true
        uploadLoading  = true
        loadingType = .uploading
        
        let UserMessageId = UUID()
        
        uploadTask = Task {
            do {
                print("自动上传 \(newPickedImages.count) 张初始图片...",threadManager.getCurrentThreadId())
                for image in newPickedImages {
                    do {
                        // 调用你提供的公共压缩方法
                        let processed = try compressionUIImage(originalImage: image, name: nil)
                        handlePickedIamges.append((processed.image, processed.fileName))
                    } catch {
                        // 如果单张图片处理失败，打印日志并跳过
                        print("⚠️ 警告：一张图片处理失败，已跳过。错误: \(error.localizedDescription)")
                    }
                }
                
                
                // 如果所有图片都处理失败了
                guard !handlePickedIamges.isEmpty else {
                    // 抛出一个错误，会在下面的 catch 块中被捕获
                    throw NSError(domain: "ImageProcessingError", code: -2, userInfo: [NSLocalizedDescriptionKey: "所有图片都处理失败，无法上传。"])
                }
                
                
                handleUserFirestMessage( id:UserMessageId,images: [], localImages: handlePickedIamges)
                
                // 检查任务是否在耗时的图片处理后被取消了
                try Task.checkCancellation()
                
                let results = try await fileService.uploadImages(
                    images: handlePickedIamges,
                    threadId: threadManager.getCurrentThreadId(),
                    format: .jpeg(compressionQuality: 0.8)
                )
                try Task.checkCancellation()
                print("✅ \(results.count) 张图片自动上传成功。")
                handleUserFirestMessage( id:UserMessageId,images: results, localImages: handlePickedIamges)
                
                self.uploadedImageFiles.append(contentsOf: results)
            } catch is CancellationError {
                print("图片上传任务被取消。")
            } catch {
                print("❌ 图片上传失败: \(error.localizedDescription)")
                self.errorMessage = "图片上传失败: \(error.localizedDescription)"
                self.showError = true
            }
            
            resetLoadingState()
            self.uploadTask = nil
            
            

            //上传完毕后 发送请求 进行识别
            sendMessage()
        }
    }
    
    private func handleUserFirestMessage(id:UUID,images: [UploadImagesRes],localImages:[(UIImage,String)]) {
        //为空，添加本地图片数据
        if images.isEmpty {
            let messageImages = localImages.map { serverImage -> MessageImage in
                // 如果找到了，matchedLocalImage 就是对应的 UIImage；如果没找到，它就是 nil。
                // 这正是 MessageImage 初始化器所期望的。
                return MessageImage(
                    fileId: id.uuidString,
                    fileName: serverImage.1,
                    fileUrl: "",
                    localImage: serverImage.0 // <-- 将查找到的图片或 nil 传进去
                )
            }
            
            let userMessage = ChatMessageModel(id: id, content: "", isUser: true, images: messageImages.isEmpty ? nil : messageImages)
            
            if !messages.contains(where: { $0.id == id }) {
                messages.append(userMessage)
            }
        } else {
            //修改对应数据
            //根据 fileid == id &&images中的originalName 与 fileName 匹配进行修改 ChatMessageModel 与 MessageImage
            guard let messageIndex = messages.firstIndex(where: { $0.id == id }) else {
                print("⚠️ 警告：无法找到要更新的消息，ID: \(id.uuidString)")
                return
            }
            
            // 2. 为了更高效地匹配，将服务器返回的 images 数组转换成一个字典
            //    Key: originalName, Value: UploadImagesRes
            let serverImageMap = Dictionary(uniqueKeysWithValues: images.map { ($0.originalName, $0) })
            
            // 3. 获取旧的消息实例
            var messageToUpdate = messages[messageIndex]
            
            // 4. 更新消息中的图片数组
            if var existingImages = messageToUpdate.images {
                // 遍历每一张需要更新的图片
                for i in 0..<existingImages.count {
                    let oldImage = existingImages[i]
                    
                    // 在字典中查找匹配的服务器数据
                    if let serverData = serverImageMap[oldImage.fileName] {
                        // 如果找到了，用服务器数据更新这张图片的信息
                        // 因为 MessageImage 是 struct，我们创建一个新实例来替换
                        existingImages[i] = MessageImage(
                            fileId: String(serverData.id), // 使用服务器返回的真实 fileId
                            fileName: serverData.originalName,
                            fileUrl: serverData.url, // 使用服务器返回的真实 url
                            localImage: oldImage.localImage // 保留本地预览图
                        )
                    }
                }
                // 将更新后的图片数组赋值回消息模型
                messageToUpdate.images = existingImages
            }
            // 5. 用更新后的消息模型替换掉数组中的旧模型
            messages[messageIndex] = messageToUpdate
        }
    }

    
    /// 发送消息（核心入口）
    /// 此方法会先检查是否有新图片需要上传，完成后再发送聊天请求。
    // MARK: sendMessage
    func sendMessage() {
        guard !isLoading else {
            print("系统正忙，请稍后再试。")
            return
        }
        if !uploadedImageFiles.isEmpty {
            // **情况1: 发送图片**
            // 文本内容强制为空字符串，符合业务规则。
            let textForThisMessage = ""
            print("dasdasdasdads",uploadedImageFiles.count)
            let imagesForThisMessage = uploadedImageFiles
            
            // 设置加载状态
            isLoading = true
            loadingType = .streaming
            
            // 添加用户消息（只有图片，没有文本）
//            addUserMessage(content: textForThisMessage, images: imagesForThisMessage, localImages: handlePickedIamges)
            
            // 清空暂存区和输入框
            self.uploadedImageFiles.removeAll()
            self.inputText = ""
            
            // 发送网络请求
            Task {
                await sendImageRecognitionRequest(
                    message: textForThisMessage,
                    stagedFiles: imagesForThisMessage,
                    isRetry: false,
                    isFirst: true
                )
            }
            
        } else {
            // **情况2: 发送纯文本消息**
            let trimmedInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 必须有文本内容才能发送
            guard !trimmedInput.isEmpty else { return }
            
            // 设置加载状态
            isLoading = true
            loadingType = .streaming
            
            // 添加用户消息（只有文本，没有图片）
            addUserMessage(content: trimmedInput, images: []) // 传入空数组
            
            // 清空输入框
            self.inputText = ""
            
            // 发送网络请求
            Task {
                await sendImageRecognitionRequest(
                    message: trimmedInput,
                    stagedFiles: [], // 传入空数组
                    isRetry: false,
                    isFirst: false
                )
            }
        }
    }
    // MARK: - 私有方法 (消息管理、状态清理等)
    
    
    /// 执行历史对话加载
    // MARK: performLoadHistoryChat
    private func performLoadHistoryChat(threadId: String) async {
        do {
            let historyItems = try await chatService.getHistoryDetail(threadId: threadId, graphId: chatMode_graphId.rawValue)

            await MainActor.run {
                let chatMessages = convertHistoryItemsToChatMessages(historyItems)

                self.messages = chatMessages

                self.setCurrentThread(threadId: threadId)

                self.isLoading = false
                self.loadingType = .none
            }

        } catch {
            await MainActor.run {
                self.isLoading = false
                self.loadingType = .none
                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }
                self.errorMessage = "加载历史对话失败: \(errorMsg)"
                self.showError = true

                print("❌ ChatController: 加载历史对话失败 - \(error)")
            }
        }
    }
    
    private func convertHistoryItemsToChatMessages(_ historyItems: [HistoryDetailItem]) -> [ChatMessageModel] {
        var chatMessages: [ChatMessageModel] = []

        for item in historyItems {
            if let chatMessage = item.handleMessage() {
                chatMessages.append(chatMessage)
            }
        }

        return chatMessages
    }
    
    /// 设置当前线程ID（用于历史对话）
    private func setCurrentThread(threadId: String) {
        threadManager.setCurrentThread(threadId: threadId, isFirst: false)
    }
    
    /// 创建请求对象。
    private func createImageRecognitionRequest(message: String, stagedFiles: [UploadImagesRes], isRetry: Bool,isFirst: Bool) -> ImageRecognitionReq {
        let threadId = threadManager.getCurrentThreadId()
//        let isFirstMessageInSession = threadManager.getIsFirstMessage() && !isRetry
        
        return ImageRecognitionReq(
            thread_id: threadId,
            uploaded_images: stagedFiles.count == 0 ? nil : stagedFiles.map { file in
                UploadedImageInfo(
                    file_id: String(file.id),
                    file_name: file.name,
                    file_url: file.url
                )
            },
            vision_type: selectedVisionType,
            messages: message,
            is_first: isFirst
//            parentId: isRetry ? threadId : nil,
            
        )
    }
    
    /// 发送图片识别请求的内部实现。
    private func sendImageRecognitionRequest(message: String, stagedFiles: [UploadImagesRes], isRetry: Bool,isFirst:Bool) async {
        if !threadManager.hasActiveSession() {
            threadManager.startNewSession()
        }
        
        let request = createImageRecognitionRequest(
            message: message,
            stagedFiles: stagedFiles,
            isRetry: isRetry,
            isFirst:isFirst
        )
        
        currentStreamTask?.cancel()
        currentStreamTask = Task { @MainActor in
            defer { currentStreamTask = nil }
            
            let stream = chatService.startImageRecognitionChatStream(req: request)
            
            ensureCurrentAIMessage()
            
            for await response in stream {
                if Task.isCancelled {
                    print("ImageRecognitionChatController: 任务被取消。")
                    break
                }
                
                handleImageRecognitionStreamResponse(response)
            }
            
            handleStreamCompleted()
        }
    }
    
    /// 用于处理传入流数据的顶层分发函数。
    private func handleImageRecognitionStreamResponse(_ response: Res<ImageRecognitionRes>) {
        
        print(response)
        
        // 1. 首先检查业务错误码
        guard response.code == 200 else {
            handleError(BusinessError(code: response.code, message: "服务返回错误")) // 假设顶层Res没有msg字段
            currentStreamTask?.cancel()
            return
        }
        
        // 2. 处理成功返回的数据，根据 ImageRecognitionRes 的类型进行分发
        switch response.data {
        case .status(let statusResponse):
            // 将状态事件分发给状态处理器
            handleRecognitionStatus(statusResponse)
            
        case .text(let textResponse):
            // 将文本内容分发给内容处理器
            handleRecognitionContent(textResponse.content ?? "")
        }
    }
    
    /// 处理图片识别过程中的状态事件。
    private func handleRecognitionStatus(_ status: ImageRecognitionStreamStatusRes) {
        print("ImageRecognitionChatController: 收到状态事件 - event: \(status.event ?? ""), node: \(status.node ?? "")")
        
        // 你可以根据不同的事件更新UI，比如显示一个更具体的状态文本
        switch status.event {
        case "on_chain_start":
            print("识别流程开始...")
            // 可以在这里更新 currentAIMessage 的内容为一个更友好的提示
//            if let currentMessage = currentAIMessage {
//                currentAIMessage = ChatMessageModel(id: currentMessage.id, content: "正在分析图片...", isUser: false)
//                updateCurrentMessageInList()
//            }
        case "on_chain_end":
            print("识别流程结束。")
            // 流程结束，等待最终文本，一般无需操作
        default:
            print("未知的状态事件: \(status.event ?? "")")
        }
    }
    
    /// 处理从流中接收到的实际文本内容块。
    private func handleRecognitionContent(_ contentChunk: String) {
        guard let currentMessage = currentAIMessage else { return }
        
        // 如果之前的状态是“正在分析图片...”，现在收到文本，应该清除提示，开始累加内容
//        let baseContent = currentMessage.content == "正在分析图片..." ? "" : currentMessage.content
        let baseContent = currentMessage.content == "" ? "" : currentMessage.content

        let updatedContent = baseContent + contentChunk
        
        // 用新内容更新消息对象
        currentAIMessage = ChatMessageModel(id: currentMessage.id, content: updatedContent, isUser: false)
        
        // 刷新 UI
        updateCurrentMessageInList()
    }
    

    

    
    /// 将用户消息添加到列表中。
    private func addUserMessage(content: String, images: [UploadImagesRes], localImages : [(UIImage,String)]? = nil ) {
                
        // --- 1. 创建一个从文件名到 UIImage 的查找字典 (核心优化点) ---
        // 这个字典让我们能够根据文件名快速找到对应的本地图片。
        let localImageMap: [String: UIImage] = Dictionary(
            uniqueKeysWithValues: (localImages ?? []).map { (image, name) in
                return (name, image)
            }
        )
        
        // --- 2. 遍历服务器返回的 images 数组，并创建 MessageImage 实例 ---
        let messageImages = images.map { serverImage -> MessageImage in
            // 从字典中查找与服务器文件名匹配的本地图片
            let matchedLocalImage = localImageMap[serverImage.originalName]
            
            // 如果找到了，matchedLocalImage 就是对应的 UIImage；如果没找到，它就是 nil。
            // 这正是 MessageImage 初始化器所期望的。
            return MessageImage(
                fileId: String(serverImage.id),
                fileName: serverImage.originalName,
                fileUrl: serverImage.url,
                localImage: matchedLocalImage // <-- 将查找到的图片或 nil 传进去
            )
        }
        
        let userMessage = ChatMessageModel(content: content, isUser: true, images: messageImages.isEmpty ? nil : messageImages)
        messages.append(userMessage)
    }
    
    
    
    //MARK: - resetLoadingState
    /// 重置加载状态。
    private func resetLoadingState() {
        isLoading = false
        uploadLoading = false
        loadingType = .none
    }
    
    
    //MARK: - handleError
    /// 统一处理错误情况
    private func handleError(_ error: Error) {
        let errorText: String
        if let businessError = error as? BusinessError {
            errorText = businessError.message
        } else {
            errorText = error.localizedDescription
        }
        
        // 如果正在流式传输，用错误消息替换占位符
        if let currentMsg = currentAIMessage, let index = messages.firstIndex(where: { $0.id == currentMsg.id }) {
            messages[index] = ChatMessageModel(
                id: currentMsg.id,
                content: "错误: \(errorText)",
                isUser: false,
                type: .error
            )
        }
        
        errorMessage = errorText
        showError = true
        resetLoadingState()
        
        // 重置流状态
        currentAIMessage = nil
        isRetryingMessage = false
        retryingMessageId = nil
    }
    
    //MARK: - handleStreamResponse
    /// 处理流式响应。
    private func handleStreamResponse(_ contentChunk: String) {
        ensureCurrentAIMessage()
        if let currentMessage = currentAIMessage {
            let updatedContent = currentMessage.content + contentChunk
            currentAIMessage = ChatMessageModel(id: currentMessage.id, content: updatedContent, isUser: false)
            updateCurrentMessageInList()
        }
        guard let currentMessage = currentAIMessage else {
            print("⚠️ 收到流数据但 currentAIMessage 为空，忽略。")
            return
        }
        
        let updatedContent = currentMessage.content + contentChunk
        currentAIMessage = ChatMessageModel(id: currentMessage.id, content: updatedContent, isUser: false)
        updateCurrentMessageInList()
        
    }
    
    //MARK: - handleStreamCompleted
    /// 流式响应结束后的处理。
    private func handleStreamCompleted() {
        print("ImageRecognitionChatController: 流处理流程已结束。")
        finishCurrentAIMessage()
        
        isLoading = false
        loadingType = .none
        
        threadManager.continueCurrentSession()
        resetLoadingState()
        
        if isRetryingMessage {
            isRetryingMessage = false
            retryingMessageId = nil
        }
    }
    
    //MARK: - finishCurrentAIMessage
    /// 在流结束后，完成对当前AI消息的处理。
    private func finishCurrentAIMessage() {
        if let currentMessage = currentAIMessage, currentMessage.content == "正在分析图片..." {
            // 如果流结束了，但AI消息仍然是初始提示，说明没有收到任何有效内容
            // 可以在这里将其标记为错误或空消息
            if let index = messages.firstIndex(where: { $0.id == currentMessage.id }) {
                messages[index] = ChatMessageModel(id: currentMessage.id, content: "[未返回有效内容]", isUser: false, type: .error)
            }
        }
        
        currentAIMessage = nil
    }
    
    //MARK: - ensureCurrentAIMessage
    /// 确保当前有一个用于接收流式数据的AI消息对象。
    private func ensureCurrentAIMessage() {
        if currentAIMessage == nil {
            let aiMessage = ChatMessageModel(content: "", isUser: false)
            currentAIMessage = aiMessage
            if isRetryingMessage, let msgId = retryingMessageId, let index = messages.firstIndex(where: { $0.id == msgId }) {
                messages[index] = aiMessage
            } else {
                messages.append(aiMessage)
            }
        }
    }
    
    //MARK: - updateCurrentMessageInList
    /// 在列表中更新当前AI消息。
    private func updateCurrentMessageInList() {
        guard let currentMessage = currentAIMessage else { return }
        if let lastIndex = messages.lastIndex(where: { $0.id == currentMessage.id }) {
            messages[lastIndex] = currentMessage
        }else {
            print("⚠️ 无法在列表中找到要更新的当前AI消息。")
        }
    }
    
   
    
    /// 显示复制成功提示
    private func showCopySuccessToast() {
        ToastManager.shared.showSuccess("复制成功")
    }
    
    
}
    
   

    
