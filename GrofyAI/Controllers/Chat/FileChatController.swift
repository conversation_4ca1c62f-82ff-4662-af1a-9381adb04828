import Combine
import Foundation
import SwiftUI

// MARK: - 文件聊天控制器

@MainActor
final class FileChatController: ObservableObject {
    /// 稳定消息列表
    @Published private(set) var stableMessages: [ChatMessageModel] = []

    /// 流式消息
    @Published private(set) var streamingMessage: ChatMessageModel?

    /// 文件分析消息（专用管理）
    @Published private(set) var analysisMessage: ChatMessageModel?

    /// 流式内容缓冲区
    @Published private(set) var streamingContent = ""

    /// 缓存第一条消息是否为文件分析消息
    @Published private(set) var hasFileAnalysisMessage = false

    // MARK: - UI 状态管理

    @Published var inputText = ""
    @Published var isLoading = false
    @Published var loadingType: ChatLoadingType = .none
    @Published var errorMessage = ""
    @Published var showError = false

    // MARK: - 文件聊天专用状态

    @Published var isChatLoading = false
    @Published var chatLoadingType: ChatLoadingType = .none
    @Published var isAnalysisLoading = false
    @Published var selectedTab: FileAnalysisTab = .snippet

    // MARK: - 内部组件

    private let streamingManager = StreamingContentManager()
    private let messageManager = MessageDataManager()
    private let analysisManager = FileAnalysisManager()

    // MARK: - 核心服务依赖

    private let chatService = ChatService()
    private let threadManager = ThreadManager.shared

    // MARK: - 流式处理状态

    private var currentStreamTask: Task<Void, Never>?
    private var currentStreamingMessageId: UUID?

    // MARK: - 文件相关状态

    private var knowledgeId: Int?
    private var fileName: String?
    private var hasProcessedInitialAnalysis = false

    // MARK: - 重试机制状态

    private var isRetryingMessage = false
    private var retryingMessageId: UUID?
    private var retryingVariantIndex: Int?

    init() {
        setupStreamingManager()
    }

    deinit {
        currentStreamTask?.cancel()
        currentStreamTask = nil
        streamingManager.reset()
        analysisManager.reset()
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - 公共接口

    /// 设置文件信息
    func setFileInfo(knowledgeId: Int, fileName: String?) {
        self.knowledgeId = knowledgeId
        self.fileName = fileName
    }

    /// 开始首次文件分析
    func startInitialAnalysis() {
        guard knowledgeId != nil else {
            handleError(BusinessError(code: -1, message: "缺少文件ID"))
            return
        }

        hasProcessedInitialAnalysis = true
        isAnalysisLoading = true
        errorMessage = ""
        showError = false

        // 重置分析状态
        analysisManager.reset()

        threadManager.startNewSession()

        // 确保分析消息存在
        ensureAnalysisMessage()

        Task {
            await sendInitialAnalysisRequest()
        }
    }

    /// 发送普通聊天消息
    func sendMessage() {
        let trimmedInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty, !isChatLoading else { return }

        let userMessage = trimmedInput
        inputText = ""

        addUserMessage(content: userMessage)
        startStreamingResponse(for: userMessage)
    }

    /// 停止流式响应
    func stopStreaming() {
        currentStreamTask?.cancel()
        currentStreamTask = nil

        Task {
            await EventSourceAdapter.shared.disconnect()
            await MainActor.run {
                self.streamingManager.forceFlush { [weak self] remainingContent in
                    if !remainingContent.isEmpty {
                        self?.updateStreamingMessageContent(remainingContent)
                    }
                }

                self.isLoading = false
                self.loadingType = .none
                self.isChatLoading = false
                self.chatLoadingType = .none
                self.isAnalysisLoading = false
                self.streamingContent = ""
                self.finalizeCurrentStreamingMessage()
            }
        }
    }

    /// 清理状态
    func clearState() {
        stableMessages.removeAll()
        streamingMessage = nil
        analysisMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil
        hasFileAnalysisMessage = false

        inputText = ""
        isLoading = false
        loadingType = .none
        isChatLoading = false
        chatLoadingType = .none
        isAnalysisLoading = false
        errorMessage = ""
        showError = false
        selectedTab = .snippet
        hasProcessedInitialAnalysis = false

        // 重置管理器
        streamingManager.reset()
        messageManager.clearCache()
        analysisManager.reset()

        currentStreamTask?.cancel()
        currentStreamTask = nil

        threadManager.clearCurrentSession()
    }

    /// 重试消息
    func retryMessage(messageId: UUID) {
        guard let messageIndex = findMessageIndex(by: messageId),
              !stableMessages[messageIndex].isUser
        else { return }

        let messageToRetry = stableMessages[messageIndex]
        setupRetryState(for: messageToRetry)

        if let userMessageIndex = findPreviousUserMessage(before: messageIndex) {
            let userMessage = stableMessages[userMessageIndex].content
            startRetryRequest(for: userMessage, messageId: messageId)
        }
    }

    /// 复制消息
    func copyMessage(_ message: ChatMessageModel) {
        UIPasteboard.general.string = message.content
        ToastManager.shared.showSuccess("消息已复制")
    }

    /// 分享消息
    func shareMessage(_ message: ChatMessageModel) {
        // TODO: 实现分享功能
    }

    /// 切换文件分析标签页
    func switchAnalysisTab(to tab: FileAnalysisTab) {
        selectedTab = tab

        if let analysisMsg = analysisMessage,
           let analysisResult = analysisMsg.fileAnalysisResult
        {
            updateAnalysisMessage(with: analysisResult)
        }
    }

    /// 加载历史对话
    func loadHistoryChat(threadId: String) {
        stopStreaming()

        stableMessages.removeAll()
        streamingMessage = nil
        analysisMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil
        errorMessage = ""
        showError = false
        hasProcessedInitialAnalysis = true

        messageManager.clearCache()
        analysisManager.reset()

        isLoading = true
        loadingType = .loadingHistory

        Task {
            await performLoadHistoryChat(threadId: threadId)
        }
    }

    /// 获取消息变体信息
    func getMessageVariantInfo(messageId: UUID) -> (current: Int, total: Int)? {
        guard let message = findMessage(by: messageId),
              let variants = message.variants
        else {
            return nil
        }

        return (current: message.currentVariantIndex, total: variants.count)
    }

    /// 检查是否正在重试指定消息
    func isRetryingMessage(messageId: UUID) -> Bool {
        return isRetryingMessage && retryingMessageId == messageId
    }

    /// 检查是否正在重试任何消息
    func isCurrentlyRetrying() -> Bool {
        return isRetryingMessage
    }

    /// 获取当前重试的消息ID（仅用于UI）
    var currentRetryingMessageId: UUID? {
        return isRetryingMessage ? retryingMessageId : nil
    }

    /// 获取重试期间的变体信息
    func getRetryVariantInfo() -> (current: Int, total: Int)? {
        guard isRetryingMessage,
              let retryingId = retryingMessageId,
              let message = findMessage(by: retryingId)
        else {
            return nil
        }

        let existingVariantsCount = message.variants?.count ?? 1
        let totalCount = existingVariantsCount + 1

        return (current: totalCount - 1, total: totalCount)
    }

    /// 切换消息变体（支持正常和重试场景）
    func switchMessageVariant(messageId: UUID, variantIndex: Int) {
        guard let messageIndex = findMessageIndex(by: messageId),
              let variants = stableMessages[messageIndex].variants,
              variantIndex < variants.count
        else {
            return
        }

        let selectedVariant = variants[variantIndex]
        let updatedMessage = createMessageFromVariant(
            originalMessage: stableMessages[messageIndex],
            variant: selectedVariant,
            variantIndex: variantIndex
        )

        stableMessages[messageIndex] = updatedMessage

        // 如果是重试场景，记录用户选择
        if isRetryingMessage, retryingMessageId == messageId {
            retryingVariantIndex = variantIndex
        }
    }

    /// 切换到新的历史记录
    func switchToHistoryRecord(knowledgeId: Int, threadId: String, fileName: String?) {
        self.knowledgeId = knowledgeId
        self.fileName = fileName
        loadHistoryChat(threadId: threadId)
    }
}

// MARK: - 私有实现方法

extension FileChatController {
    private func setupStreamingManager() {
        streamingManager.reset()
        analysisManager.reset()
    }

    // MARK: - 消息管理

    private func addUserMessage(content: String) {
        let userMessage = ChatMessageModel(
            content: content,
            isUser: true,
            chatMode: .rag
        )

        let newIndex = stableMessages.count
        stableMessages.append(userMessage)

        messageManager.addMessageToCache(message: userMessage, at: newIndex)
    }

    private func createStreamingMessage() -> ChatMessageModel {
        return ChatMessageModel(
            content: "",
            isUser: false,
            type: .text,
            chatMode: .rag
        )
    }

    private func finalizeCurrentStreamingMessage() {
        guard let streaming = streamingMessage else { return }

        // 将流式消息转换为稳定消息
        let finalMessage = ChatMessageModel(
            id: streaming.id,
            content: streaming.content,
            isUser: false,
            type: streaming.type,
            searchResults: streaming.searchResults,
            files: streaming.files,
            images: streaming.images,
            reasoningContent: streaming.reasoningContent,
            toolCallStatus: streaming.toolCallStatus,
            isToolActive: streaming.isToolActive,
            variants: streaming.variants,
            currentVariantIndex: streaming.currentVariantIndex,
            chatMode: streaming.chatMode
        )

        let newIndex = stableMessages.count
        stableMessages.append(finalMessage)

        // 使用高效的增量缓存更新
        messageManager.addMessageToCache(message: finalMessage, at: newIndex)

        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
    }

    // MARK: - 消息查找辅助方法

    private func findMessage(by id: UUID) -> ChatMessageModel? {
        if let index = findMessageIndex(by: id) {
            return stableMessages[index]
        }
        return nil
    }

    private func findMessageIndex(by id: UUID) -> Int? {
        return messageManager.findMessageIndex(by: id, in: stableMessages)
    }

    private func findPreviousUserMessage(before index: Int) -> Int? {
        for i in stride(from: index - 1, through: 0, by: -1) {
            if stableMessages[i].isUser {
                return i
            }
        }
        return nil
    }

    private func createMessageFromVariant(
        originalMessage: ChatMessageModel,
        variant: MessageVariantModel,
        variantIndex: Int
    ) -> ChatMessageModel {
        return ChatMessageModel(
            id: originalMessage.id,
            content: variant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            searchResults: variant.searchResults,
            files: variant.files,
            images: variant.images,
            reasoningContent: variant.thinkingContent,
            toolCallStatus: originalMessage.toolCallStatus,
            isToolActive: originalMessage.isToolActive,
            variants: originalMessage.variants,
            currentVariantIndex: variantIndex,
            chatMode: originalMessage.chatMode
        )
    }

    // MARK: - 流式处理核心逻辑

    private func startStreamingResponse(for message: String) {
        isChatLoading = true
        chatLoadingType = .streaming
        errorMessage = ""
        showError = false

        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        if !threadManager.hasActiveSession() {
            threadManager.startNewSession()
        }

        Task {
            await sendStreamingRequest(message: message)
        }
    }

    private func startRetryRequest(for message: String, messageId: UUID) {
        isChatLoading = true
        chatLoadingType = .streaming

        streamingMessage = createStreamingMessage()
        currentStreamingMessageId = streamingMessage?.id
        streamingContent = ""

        Task {
            await sendRetryRequest(message: message)
        }
    }

    // MARK: - 网络请求方法

    private func sendInitialAnalysisRequest() async {
        guard let knowledgeId else {
            await MainActor.run {
                self.handleError(BusinessError(code: -1, message: "缺少文件ID"))
            }
            return
        }

        let request = createInitialAnalysisRequest(knowledgeId: knowledgeId)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startFileChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleAnalysisStreamResponse(response)
            }

            handleAnalysisStreamCompleted()
        }
    }

    private func sendStreamingRequest(message: String) async {
        guard let knowledgeId else {
            await MainActor.run {
                self.handleError(BusinessError(code: -1, message: "缺少文件ID"))
            }
            return
        }

        let request = createChatRequest(message: message, knowledgeId: knowledgeId)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startFileChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleStreamResponse(response)
            }

            handleStreamCompleted()
        }
    }

    private func sendRetryRequest(message: String) async {
        guard let knowledgeId else {
            await MainActor.run {
                self.handleError(BusinessError(code: -1, message: "缺少文件ID"))
            }
            return
        }

        let request = createRetryRequest(message: message, knowledgeId: knowledgeId)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startFileChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleStreamResponse(response)
            }

            handleRetryCompleted()
        }
    }

    // MARK: - 请求创建方法

    private func createInitialAnalysisRequest(knowledgeId: Int) -> UnifiedChatCompletionReq {
        let threadId = threadManager.getCurrentThreadId()

        return chatService.createFileRequest(
            knowledgeId: knowledgeId,
            message: "",
            threadId: threadId,
            isFirst: true
        )
    }

    private func createChatRequest(message: String, knowledgeId: Int) -> UnifiedChatCompletionReq {
        let threadId = threadManager.getCurrentThreadId()
        let isFirst = threadManager.getIsFirstMessage()

        return chatService.createFileRequest(
            knowledgeId: knowledgeId,
            message: message,
            threadId: threadId,
            isFirst: isFirst
        )
    }

    private func createRetryRequest(message: String, knowledgeId: Int) -> UnifiedChatCompletionReq {
        let threadId = threadManager.getCurrentThreadId()

        return chatService.createFileRequest(
            knowledgeId: knowledgeId,
            message: message,
            threadId: threadId,
            parentId: threadId,
            isFirst: false
        )
    }

    // MARK: - 重试状态管理

    private func setupRetryState(for message: ChatMessageModel) {
        isRetryingMessage = true
        retryingMessageId = message.id
        retryingVariantIndex = message.currentVariantIndex
        objectWillChange.send()
    }

    // MARK: - 流式响应处理

    private func handleAnalysisStreamResponse(_ response: Res<ChatCompletionPes>) {
        guard response.code == 200 || response.msg == "SSE_OK" else {
            handleError(BusinessError(code: response.code, message: response.msg))
            return
        }

        let chatData = response.data

        switch chatData {
        case .event(_, let type, _, _, let content):
            if type == .ragIndex {
                handleRagIndexContent(content)
            }
        default:
            break
        }
    }

    private func handleStreamResponse(_ response: Res<ChatCompletionPes>) {
        guard response.code == 200 || response.msg == "SSE_OK" else {
            handleError(BusinessError(code: response.code, message: response.msg))
            return
        }

        let chatData = response.data

        switch chatData {
        case .text(_, let content):
            accumulateTextContent(content)
        case .reasoningText(_, let content):
            accumulateReasoningContent(content)
        case .toolDeepSearch(_, let searchResults):
            accumulateSearchResults(searchResults)
        case .event(_, let type, let event, let node, let content):
            let contentString = content?.value as? String
            handleEventResponse(type: type, event: event, node: node, content: contentString)
        }
    }

    // MARK: - 内容积累方法

    private func handleRagIndexContent(_ content: AnyCodable?) {
        guard let content,
              let jsonString = content.value as? String
        else {
            return
        }

        if let newResult = analysisManager.parseAnalysisResult(jsonString) {
            updateAnalysisMessage(with: newResult)
        }
    }

    private func accumulateTextContent(_ content: String) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        streamingManager.appendContent(content) { [weak self] bufferedContent in
            self?.updateStreamingMessageContent(bufferedContent)
        }
    }

    private func accumulateReasoningContent(_ content: String) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        guard let currentMessage = streamingMessage else { return }

        let updatedReasoningContent = (currentMessage.reasoningContent ?? "") + content

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: .reasoning,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: updatedReasoningContent,
            toolCallStatus: currentMessage.toolCallStatus,
            isToolActive: currentMessage.isToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func accumulateSearchResults(_ searchResults: [SearchResult]) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        guard let currentMessage = streamingMessage else { return }

        var allSearchResults = currentMessage.searchResults ?? []
        for result in searchResults {
            if !allSearchResults.contains(where: { $0.url == result.url }) {
                allSearchResults.append(result)
            }
        }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: .searchResult,
            searchResults: allSearchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: currentMessage.toolCallStatus,
            isToolActive: currentMessage.isToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func updateStreamingMessageContent(_ bufferedContent: String) {
        guard let currentMessage = streamingMessage else { return }

        let updatedContent = currentMessage.content + bufferedContent

        streamingContent = updatedContent

        // 处理工具调用状态
        var updatedToolCallStatus = currentMessage.toolCallStatus
        var updatedIsToolActive = currentMessage.isToolActive

        if let toolStatus = currentMessage.toolCallStatus, !toolStatus.isEmpty, currentMessage.isToolActive {
            updatedIsToolActive = false
            updatedToolCallStatus = "工具调用完毕"
        }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: updatedContent,
            isUser: false,
            type: .text,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: updatedToolCallStatus,
            isToolActive: updatedIsToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    private func ensureStreamingMessage() {
        if streamingMessage == nil {
            streamingMessage = createStreamingMessage()
            currentStreamingMessageId = streamingMessage?.id
        }
    }

    // MARK: - 事件处理

    private func handleEventResponse(type: EventResponse.EventType, event: String?, node: String?, content: String?) {
        guard streamingMessage != nil else {
            ensureStreamingMessage()
            return
        }

        switch type {
        case .status:
            if event == "on_tool_start" {
                let toolStatus = content ?? "工具调用中..."
                updateStreamingMessageToolStatus(status: toolStatus, isActive: true)
            } else if event == "on_tool_end" {
                let toolStatus = content ?? "工具调用完毕"
                updateStreamingMessageToolStatus(status: toolStatus, isActive: false)
            }

        case .ragIndex:
            // RAG 索引事件已在 handleAnalysisStreamResponse 中处理
            break
        }
    }

    private func updateStreamingMessageToolStatus(status: String, isActive: Bool) {
        guard let currentMessage = streamingMessage else { return }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: currentMessage.content,
            isUser: false,
            type: currentMessage.type,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: status,
            isToolActive: isActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    // MARK: - 完成处理

    private func handleAnalysisStreamCompleted() {
        // 确保分析消息已更新到最终状态
        let analysisResult = analysisManager.getCurrentResult()
        if analysisResult.hasContent {
            updateAnalysisMessage(with: analysisResult)
        } else {
            handleError(BusinessError(code: -1, message: "分析完成但未收到有效结果"))
        }

        // 流完成后的状态清理
        currentStreamTask = nil
        isLoading = false
        loadingType = .none
        isAnalysisLoading = false
    }

    private func handleStreamCompleted() {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        finalizeCurrentStreamingMessage()

        isChatLoading = false
        chatLoadingType = .none
        streamingManager.reset()

        threadManager.continueCurrentSession()

        if isRetryingMessage {
            finishRetryProcess()
        }

        // 流式响应完成时更新缓存状态
        hasFileAnalysisMessage = analysisMessage != nil
    }

    private func handleRetryCompleted() {
        // 强制刷新缓冲区确保最后部分内容不丢失
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        if let retryingId = retryingMessageId,
           let streaming = streamingMessage
        {
            finishRetryAsVariant(retryingId: retryingId, newMessage: streaming)
        }

        isChatLoading = false
        chatLoadingType = .none
        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingManager.reset()

        threadManager.continueCurrentSession()
        finishRetryProcess()
    }

    private func finishRetryProcess() {
        isRetryingMessage = false
        retryingMessageId = nil
        retryingVariantIndex = nil
        objectWillChange.send()
    }

    private func finishRetryAsVariant(retryingId: UUID, newMessage: ChatMessageModel) {
        guard let messageIndex = findMessageIndex(by: retryingId) else { return }

        let originalMessage = stableMessages[messageIndex]
        var allVariants = originalMessage.variants ?? []

        if allVariants.isEmpty {
            let originalVariant = MessageVariantModel(
                id: UUID().uuidString,
                content: originalMessage.content,
                modelId: nil,
                thinkingContent: originalMessage.reasoningContent,
                searchResults: originalMessage.searchResults,
                files: originalMessage.files,
                images: originalMessage.images
            )
            allVariants.append(originalVariant)
        }

        let newVariant = MessageVariantModel(
            id: UUID().uuidString,
            content: newMessage.content,
            modelId: nil,
            thinkingContent: newMessage.reasoningContent,
            searchResults: newMessage.searchResults,
            files: newMessage.files,
            images: newMessage.images
        )
        allVariants.append(newVariant)

        let currentVariantIndex = retryingVariantIndex ?? (allVariants.count - 1)
        let displayVariant = currentVariantIndex < allVariants.count ?
            allVariants[currentVariantIndex] : allVariants.last!

        let updatedMessage = ChatMessageModel(
            id: originalMessage.id,
            content: displayVariant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            searchResults: displayVariant.searchResults,
            files: displayVariant.files,
            images: displayVariant.images,
            reasoningContent: displayVariant.thinkingContent,
            toolCallStatus: newMessage.toolCallStatus,
            isToolActive: newMessage.isToolActive,
            variants: allVariants,
            currentVariantIndex: currentVariantIndex,
            chatMode: originalMessage.chatMode
        )

        stableMessages[messageIndex] = updatedMessage
        messageManager.updateIndexCache(for: stableMessages)
    }

    // MARK: - 历史记录处理

    private func performLoadHistoryChat(threadId: String) async {
        let shouldStartAnalysis = await MainActor.run {
            guard self.knowledgeId != nil else { return false }
            self.isAnalysisLoading = true
            return true
        }

        await withTaskGroup(of: Void.self) { group in
            group.addTask { [weak self] in
                await self?.loadHistoryData(threadId: threadId)
            }

            if shouldStartAnalysis {
                group.addTask { [weak self] in
                    await self?.performConcurrentAnalysis()
                }
            }

            await group.waitForAll()
        }

        await MainActor.run {
            self.isLoading = false
            self.loadingType = .none
        }
    }

    private func loadHistoryData(threadId: String) async {
        do {
            let historyItems = try await chatService.getHistoryDetail(threadId: threadId, graphId: "ragbot_graph")

            await MainActor.run {
                let chatMessages = convertHistoryItemsToFileChatMessages(historyItems)

                // 分离分析消息和普通消息
                var analysisMsg: ChatMessageModel?
                var regularMessages: [ChatMessageModel] = []

                for message in chatMessages {
                    if message.type == .fileAnalysis {
                        analysisMsg = message
                    } else {
                        regularMessages.append(message)
                    }
                }

                self.analysisMessage = analysisMsg
                self.stableMessages = regularMessages

                // 一次性重建索引缓存
                self.messageManager.updateIndexCache(for: regularMessages)

                // 更新缓存状态
                self.hasFileAnalysisMessage = analysisMsg != nil

                // 设置当前线程
                self.threadManager.setCurrentThread(threadId: threadId, isFirst: false)

                self.checkAnalysisCompleteness()
            }

        } catch {
            await MainActor.run {
                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }
                self.errorMessage = "加载历史对话失败: \(errorMsg)"
                self.showError = true
            }
        }
    }

    private func performConcurrentAnalysis() async {
        guard knowledgeId != nil else {
            await MainActor.run {
                self.isAnalysisLoading = false
            }
            return
        }

        await MainActor.run {
            // 确保分析消息存在
            self.ensureAnalysisMessage()
            // 重置分析结果
            self.analysisManager.reset()
        }

        // 执行分析请求
        await sendInitialAnalysisRequest()
    }

    private func convertHistoryItemsToFileChatMessages(_ historyItems: [HistoryDetailItem]) -> [ChatMessageModel] {
        var chatMessages: [ChatMessageModel] = []

        for item in historyItems {
            if let chatMessage = item.toFileChatMessage() {
                // 检查是否为文件分析消息
                if chatMessage.type == .fileAnalysis {
                    // 确保文件分析消息的内容根据当前selectedTab正确显示
                    if let analysisResult = chatMessage.fileAnalysisResult {
                        let content = selectedTab.getContent(from: analysisResult) ?? ""
                        let updatedMessage = ChatMessageModel(
                            id: chatMessage.id,
                            content: content,
                            isUser: false,
                            type: .fileAnalysis,
                            chatMode: .rag,
                            fileAnalysisResult: analysisResult,
                            selectedAnalysisTab: selectedTab
                        )
                        chatMessages.append(updatedMessage)
                    } else {
                        chatMessages.append(chatMessage)
                    }
                } else {
                    chatMessages.append(chatMessage)
                }
            }
        }

        // 如果历史记录中没有文件分析消息，创建一个空的
        if !chatMessages.contains(where: { $0.type == .fileAnalysis }) {
            let analysisMessage = ChatMessageModel(
                content: "",
                isUser: false,
                type: .fileAnalysis,
                chatMode: .rag,
                fileAnalysisResult: FileAnalysisResult(),
                selectedAnalysisTab: selectedTab
            )
            chatMessages.insert(analysisMessage, at: 0)
        }

        return chatMessages
    }

    // MARK: - 分析消息管理

    private func ensureAnalysisMessage() {
        if analysisMessage == nil || analysisMessage?.type != .fileAnalysis {
            analysisMessage = ChatMessageModel(
                content: "",
                isUser: false,
                type: .fileAnalysis,
                chatMode: .rag,
                fileAnalysisResult: FileAnalysisResult(),
                selectedAnalysisTab: selectedTab
            )
        }
        // 立即更新标志，确保UI能显示tab
        hasFileAnalysisMessage = true
    }

    private func updateAnalysisMessage(with result: FileAnalysisResult) {
        guard let currentAnalysisMessage = analysisMessage else {
            return
        }

        let content = selectedTab.getContent(from: result) ?? ""

        analysisMessage = ChatMessageModel(
            id: currentAnalysisMessage.id,
            content: content,
            isUser: false,
            type: .fileAnalysis,
            chatMode: .rag,
            fileAnalysisResult: result,
            selectedAnalysisTab: selectedTab
        )
    }

    // MARK: - 分析完整性检查

    private func checkAnalysisCompleteness() {
        guard let analysisMsg = analysisMessage, analysisMsg.type == .fileAnalysis else {
            return
        }

        // 检查分析结果是否有效且完整
        let analysisResult = analysisMsg.fileAnalysisResult
        let hasValidAnalysis = analysisResult?.hasContent == true
        let isCompleteAnalysis = analysisResult?.isComplete == true

        // 检查当前选中标签页的内容是否存在
        let currentTabHasContent = if let analysisResult {
            selectedTab.getContent(from: analysisResult)?.isEmpty == false
        } else {
            false
        }

        if hasValidAnalysis, isCompleteAnalysis, currentTabHasContent {
            isAnalysisLoading = false
        }
    }

    // MARK: - 错误处理

    private func handleError(_ error: BusinessError) {
        streamingManager.forceFlush { [weak self] remainingContent in
            if !remainingContent.isEmpty {
                self?.updateStreamingMessageContent(remainingContent)
            }
        }

        errorMessage = error.message
        showError = true
        isLoading = false
        loadingType = .none
        isChatLoading = false
        chatLoadingType = .none
        isAnalysisLoading = false

        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
        streamingManager.reset()

        if isRetryingMessage {
            finishRetryProcess()
        }
    }
}

/// 文件分析管理器 - 专门处理文件分析结果
private class FileAnalysisManager {
    private var currentResult = FileAnalysisResult()
    private let analysisParser = FileAnalysisResultParser()

    func parseAnalysisResult(_ jsonString: String) -> FileAnalysisResult? {
        if let newResult = analysisParser.parseResult(jsonString, currentResult: currentResult) {
            currentResult = newResult
            return newResult
        }
        return nil
    }

    func getCurrentResult() -> FileAnalysisResult {
        return currentResult
    }

    func reset() {
        currentResult = FileAnalysisResult()
    }
}

/// 流式内容管理器
private class StreamingContentManager: ObservableObject {
    private var contentBuffer = ""
    private var bufferTimer: Timer?
    private let bufferFlushInterval: TimeInterval = 0.05 // 50ms 缓冲
    private let maxBufferSize = 100 // 最大缓冲字符数

    func appendContent(_ content: String, completion: @escaping (String) -> Void) {
        contentBuffer += content

        if contentBuffer.count >= maxBufferSize {
            flushBuffer(completion: completion)
        } else {
            scheduleBufferFlush(completion: completion)
        }
    }

    private func scheduleBufferFlush(completion: @escaping (String) -> Void) {
        bufferTimer?.invalidate()
        bufferTimer = Timer.scheduledTimer(withTimeInterval: bufferFlushInterval, repeats: false) { [weak self] _ in
            self?.flushBuffer(completion: completion)
        }
    }

    private func flushBuffer(completion: @escaping (String) -> Void) {
        guard !contentBuffer.isEmpty else { return }

        let content = contentBuffer
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil

        completion(content)
    }

    func reset() {
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil
    }

    func forceFlush(completion: @escaping (String) -> Void) {
        flushBuffer(completion: completion)
    }
}

/// 消息数据管理器
private class MessageDataManager {
    private var messageIndexCache: [UUID: Int] = [:]
    private var lastCacheSize = 0

    /// 增量更新索引缓存
    func updateIndexCache(for messages: [ChatMessageModel]) {
        let currentSize = messages.count

        if currentSize == lastCacheSize, !messageIndexCache.isEmpty {
            return
        }

        if currentSize > lastCacheSize, lastCacheSize > 0 {
            for index in lastCacheSize..<currentSize {
                if index < messages.count {
                    messageIndexCache[messages[index].id] = index
                }
            }
        } else {
            messageIndexCache.removeAll(keepingCapacity: true)
            for (index, message) in messages.enumerated() {
                messageIndexCache[message.id] = index
            }
        }

        lastCacheSize = currentSize
    }

    /// 添加单个消息到缓存
    func addMessageToCache(message: ChatMessageModel, at index: Int) {
        messageIndexCache[message.id] = index
        lastCacheSize = max(lastCacheSize, index + 1)
    }

    /// 清空缓存
    func clearCache() {
        messageIndexCache.removeAll(keepingCapacity: true)
        lastCacheSize = 0
    }

    func findMessageIndex(by id: UUID, in messages: [ChatMessageModel]) -> Int? {
        if let cachedIndex = messageIndexCache[id],
           cachedIndex < messages.count,
           messages[cachedIndex].id == id
        {
            return cachedIndex
        }

        if let index = messages.firstIndex(where: { $0.id == id }) {
            messageIndexCache[id] = index
            return index
        }

        return nil
    }
}
