import Combine
import Foundation
import SwiftUI

// MARK: - 图片聊天控制器

@MainActor
final class ImageChatController: ObservableObject {
    /// 稳定消息列表
    @Published private(set) var stableMessages: [ChatMessageModel] = []

    /// 当前流式消息（独立管理，高频更新）
    @Published private(set) var streamingMessage: ChatMessageModel?

    /// 流式内容缓冲区（用于增量更新）
    @Published private(set) var streamingContent = ""

    // MARK: - UI 状态管理

    @Published var inputText = ""
    @Published var isLoading = false
    @Published var loadingType: ChatLoadingType = .none
    @Published var errorMessage = ""
    @Published var showError = false

    // MARK: - 图片生成专用功能

    @Published var imageGenerationParameters = ImageGenerationParameters()
    @Published var uploadedImageInfos: [UploadedImageReq] = []
    @Published var uploadedImageFiles: [UploadImagesRes] = []
    @Published var shouldClearUploadedImages = false

    // MARK: - 内部组件

    private let streamingManager = StreamingContentManager()
    private let messageManager = MessageDataManager()
    private let placeholderManager = PlaceholderManager()

    // MARK: - 核心服务依赖

    private let chatService = ChatService()
    private let threadManager = ThreadManager.shared
    private let fileService = FileService()

    // MARK: - 流式处理状态

    private var currentStreamTask: Task<Void, Never>?
    private var currentStreamingMessageId: UUID?

    // MARK: - 图片生成状态

    private enum ImageGenerationState {
        case idle
        case uploading
        case generating(placeholderId: UUID)
        case completed
    }

    @Published private var generationState: ImageGenerationState = .idle

    // MARK: - 重试机制状态

    private var isRetryingMessage = false
    private var retryingMessageId: UUID?
    private var retryingVariantIndex: Int?

    // MARK: - 计算属性

    var uploadedImageUrls: [String] {
        uploadedImageInfos.map(\.file_url)
    }

    var uploadedImageUrlsBinding: Binding<[String]> {
        Binding(
            get: { self.uploadedImageUrls },
            set: { newUrls in
                let currentUrls = self.uploadedImageUrls
                let removedUrls = Set(currentUrls).subtracting(Set(newUrls))

                for url in removedUrls {
                    self.removeUploadedImageUrl(url)
                }
            }
        )
    }

    /// 获取所有消息（用于视图显示）
    var messages: [ChatMessageModel] {
        var allMessages = stableMessages

        // 如果有流式消息，添加到末尾
        if let streaming = streamingMessage {
            allMessages.append(streaming)
        }

        return allMessages
    }

    init() {
        setupStreamingManager()
    }

    deinit {
        currentStreamTask?.cancel()
        currentStreamTask = nil
        streamingManager.reset()
        placeholderManager.reset()
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - 公共接口

    /// 发送消息
    func sendMessage() {
        let trimmedInput = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return }

        let userMessage = trimmedInput
        inputText = ""

        addUserMessage(content: userMessage)
        startImageGeneration(for: userMessage)
    }

    /// 处理初始消息（从其他页面跳转过来）
    func processInitialMessage(_ message: String) {
        guard !message.isEmpty else { return }

        inputText = message
        sendMessage()
    }

    /// 停止当前流式响应
    func stopStreaming() {
        currentStreamTask?.cancel()
        currentStreamTask = nil

        Task {
            await EventSourceAdapter.shared.disconnect()
            await MainActor.run {
                self.streamingManager.forceFlush { [weak self] remainingContent in
                    if !remainingContent.isEmpty {
                        self?.updateStreamingMessageContent(remainingContent)
                    }
                }

                self.isLoading = false
                self.loadingType = .none
                self.streamingContent = ""
                self.finalizeCurrentStreamingMessage()
                self.generationState = .idle
            }
        }
    }

    /// 清空消息列表
    func clearMessages() {
        stableMessages.removeAll()
        streamingMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil

        // 重置相关状态
        isLoading = false
        loadingType = .none
        errorMessage = ""
        showError = false
        generationState = .idle

        // 清空图片相关
        clearUploadedImages()
        imageGenerationParameters = ImageGenerationParameters()

        // 重置管理器
        streamingManager.reset()
        messageManager.clearCache()
        placeholderManager.reset()

        // 开始新会话
        threadManager.startNewSession()
    }

    /// 更新图片生成参数
    func updateImageGenerationParameters(_ parameters: ImageGenerationParameters) {
        print("ImageChatController: 更新图片生成参数 - 新参数: \(parameters)")
        imageGenerationParameters = parameters
    }

    /// 添加上传的图片信息
    func addUploadedImageFile(_ uploadResult: UploadImagesRes) {
        uploadedImageFiles.append(uploadResult)
        let imageInfo = UploadedImageReq(from: uploadResult)
        uploadedImageInfos.append(imageInfo)
    }

    /// 移除上传的图片
    func removeUploadedImageUrl(_ url: String) {
        uploadedImageInfos.removeAll { $0.file_url == url }
        uploadedImageFiles.removeAll { $0.url == url }
    }

    /// 清空上传的图片
    func clearUploadedImages() {
        uploadedImageInfos.removeAll()
        uploadedImageFiles.removeAll()
    }

    /// 重试消息
    func retryMessage(messageId: UUID) {
        guard let messageIndex = findMessageIndex(by: messageId),
              !stableMessages[messageIndex].isUser
        else { return }

        let messageToRetry = stableMessages[messageIndex]
        setupRetryState(for: messageToRetry)

        if let userMessageIndex = findPreviousUserMessage(before: messageIndex) {
            let userMessage = stableMessages[userMessageIndex].content
            startRetryRequest(for: userMessage, messageId: messageId)
        }
    }

    /// 复制消息内容
    func copyMessage(_ message: ChatMessageModel) {
        UIPasteboard.general.string = message.content
        ToastManager.shared.showSuccess("消息已复制")
    }

    /// 分享消息内容
    func shareMessage(_ message: ChatMessageModel) {
        // TODO: 实现分享功能
    }

    /// 加载历史聊天记录
    func loadHistoryChat(threadId: String) {
        stopStreaming()

        // 重置状态
        stableMessages.removeAll()
        streamingMessage = nil
        streamingContent = ""
        currentStreamingMessageId = nil
        errorMessage = ""
        showError = false
        generationState = .idle

        // 清空缓存
        messageManager.clearCache()
        placeholderManager.reset()

        // 设置加载状态
        isLoading = true
        loadingType = .loadingHistory

        Task {
            await performLoadHistoryChat(threadId: threadId)
        }
    }

    /// 重置当前线程
    func resetCurrentThread() {
        threadManager.clearCurrentSession()
    }

    /// 获取消息变体信息
    func getMessageVariantInfo(messageId: UUID) -> (current: Int, total: Int)? {
        guard let message = findMessage(by: messageId),
              let variants = message.variants
        else {
            return nil
        }

        return (current: message.currentVariantIndex, total: variants.count)
    }

    /// 检查是否正在重试指定消息
    func isRetryingMessage(messageId: UUID) -> Bool {
        return isRetryingMessage && retryingMessageId == messageId
    }

    /// 检查是否正在重试任何消息
    func isCurrentlyRetrying() -> Bool {
        return isRetryingMessage
    }

    /// 获取当前重试的消息ID（仅用于UI）
    var currentRetryingMessageId: UUID? {
        return isRetryingMessage ? retryingMessageId : nil
    }

    /// 获取重试期间的变体信息
    func getRetryVariantInfo() -> (current: Int, total: Int)? {
        guard isRetryingMessage,
              let retryingId = retryingMessageId,
              let message = findMessage(by: retryingId)
        else {
            return nil
        }

        let existingVariantsCount = message.variants?.count ?? 1
        let totalCount = existingVariantsCount + 1

        return (current: totalCount - 1, total: totalCount)
    }

    /// 切换消息变体（支持正常和重试场景）
    func switchMessageVariant(messageId: UUID, variantIndex: Int) {
        guard let messageIndex = findMessageIndex(by: messageId),
              let variants = stableMessages[messageIndex].variants,
              variantIndex < variants.count
        else {
            return
        }

        let selectedVariant = variants[variantIndex]
        let updatedMessage = createMessageFromVariant(
            originalMessage: stableMessages[messageIndex],
            variant: selectedVariant,
            variantIndex: variantIndex
        )

        stableMessages[messageIndex] = updatedMessage

        // 如果是重试场景，记录用户选择
        if isRetryingMessage, retryingMessageId == messageId {
            retryingVariantIndex = variantIndex
        }
    }
}

// MARK: - 私有实现方法

extension ImageChatController {
    private func setupStreamingManager() {
        streamingManager.reset()
        placeholderManager.reset()
    }

    // MARK: - 消息管理

    private func addUserMessage(content: String) {
        // 创建用户消息，如果有上传的图片，添加到消息中
        let messageImages: [MessageImage] = uploadedImageFiles.map { uploadResult in
            MessageImage(
                fileId: String(uploadResult.id),
                fileName: uploadResult.originalName.isEmpty ? uploadResult.name : uploadResult.originalName,
                fileUrl: uploadResult.url
            )
        }

        let userMessage = ChatMessageModel(
            content: content,
            isUser: true,
            images: messageImages.isEmpty ? nil : messageImages,
            chatMode: .image
        )

        let newIndex = stableMessages.count
        stableMessages.append(userMessage)

        messageManager.addMessageToCache(message: userMessage, at: newIndex)
    }

    private func createStreamingMessage() -> ChatMessageModel {
        return ChatMessageModel(
            content: "",
            isUser: false,
            type: .text,
            chatMode: .image
        )
    }

    private func finalizeCurrentStreamingMessage() {
        guard let streaming = streamingMessage else { return }

        // 将流式消息转换为稳定消息
        let finalMessage = ChatMessageModel(
            id: streaming.id,
            content: streaming.content,
            isUser: false,
            type: streaming.type,
            searchResults: streaming.searchResults,
            files: streaming.files,
            images: streaming.images,
            reasoningContent: streaming.reasoningContent,
            toolCallStatus: streaming.toolCallStatus,
            isToolActive: streaming.isToolActive,
            variants: streaming.variants,
            currentVariantIndex: streaming.currentVariantIndex,
            chatMode: streaming.chatMode
        )

        let newIndex = stableMessages.count
        stableMessages.append(finalMessage)

        // 使用高效的增量缓存更新
        messageManager.addMessageToCache(message: finalMessage, at: newIndex)

        streamingMessage = nil
        currentStreamingMessageId = nil
        streamingContent = ""
    }

    // MARK: - 消息查找辅助方法

    private func findMessage(by id: UUID) -> ChatMessageModel? {
        if let index = findMessageIndex(by: id) {
            return stableMessages[index]
        }
        return nil
    }

    private func findMessageIndex(by id: UUID) -> Int? {
        return messageManager.findMessageIndex(by: id, in: stableMessages)
    }

    private func findPreviousUserMessage(before index: Int) -> Int? {
        for i in stride(from: index - 1, through: 0, by: -1) {
            if stableMessages[i].isUser {
                return i
            }
        }
        return nil
    }

    private func createMessageFromVariant(
        originalMessage: ChatMessageModel,
        variant: MessageVariantModel,
        variantIndex: Int
    ) -> ChatMessageModel {
        return ChatMessageModel(
            id: originalMessage.id,
            content: variant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            modelId: variant.modelId,
            searchResults: variant.searchResults,
            files: variant.files,
            images: variant.images,
            reasoningContent: variant.thinkingContent,
            toolCallStatus: originalMessage.toolCallStatus,
            isToolActive: originalMessage.isToolActive,
            variants: originalMessage.variants,
            currentVariantIndex: variantIndex,
            chatMode: originalMessage.chatMode
        )
    }

    // MARK: - 流式消息更新

    private func updateStreamingMessageContent(_ bufferedContent: String) {
        guard let currentMessage = streamingMessage else { return }

        let updatedContent = currentMessage.content + bufferedContent

        streamingContent = updatedContent

        // 处理工具调用状态
        var updatedToolCallStatus = currentMessage.toolCallStatus
        var updatedIsToolActive = currentMessage.isToolActive

        if let toolStatus = currentMessage.toolCallStatus, !toolStatus.isEmpty, currentMessage.isToolActive {
            updatedIsToolActive = false
            updatedToolCallStatus = "工具调用完毕"
        }

        streamingMessage = ChatMessageModel(
            id: currentMessage.id,
            content: updatedContent,
            isUser: false,
            type: .text,
            modelId: nil,
            searchResults: currentMessage.searchResults,
            files: currentMessage.files,
            images: currentMessage.images,
            reasoningContent: currentMessage.reasoningContent,
            toolCallStatus: updatedToolCallStatus,
            isToolActive: updatedIsToolActive,
            variants: currentMessage.variants,
            currentVariantIndex: currentMessage.currentVariantIndex,
            chatMode: currentMessage.chatMode
        )
    }

    // MARK: - 图片生成核心逻辑

    private func startImageGeneration(for message: String) {
        isLoading = true
        loadingType = .streaming
        errorMessage = ""
        showError = false
        generationState = .generating(placeholderId: UUID())

        if !threadManager.hasActiveSession() {
            threadManager.startNewSession()
        }

        Task {
            await sendImageGenerationRequest(message: message)
        }
    }

    private func startRetryRequest(for message: String, messageId: UUID) {
        isLoading = true
        loadingType = .streaming
        generationState = .generating(placeholderId: UUID())

        Task {
            await sendImageRetryRequest(message: message)
        }
    }

    // MARK: - 网络请求

    private func sendImageGenerationRequest(message: String) async {
        currentStreamTask?.cancel()

        let request = createImageGenerationRequest(message: message)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startImageChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleImageGenerationResponse(response)
            }

            handleStreamCompleted()
        }
    }

    private func sendImageRetryRequest(message: String) async {
        currentStreamTask?.cancel()

        let request = createImageGenerationRetryRequest(message: message)

        currentStreamTask = Task { @MainActor in
            defer {
                currentStreamTask = nil
            }

            let stream = chatService.startImageChatStream(req: request)

            for await response in stream {
                if Task.isCancelled {
                    await EventSourceAdapter.shared.disconnect()
                    break
                }

                handleImageGenerationResponse(response)
            }

            handleRetryCompleted()
        }
    }

    // MARK: - 请求创建

    private func createImageGenerationRequest(message: String) -> ImageGenerationRequest {
        let threadId = threadManager.getCurrentThreadId()
        let isFirst = threadManager.getIsFirstMessage()

        print("ImageChatController: 创建图片生成请求 - 参数: \(imageGenerationParameters)")

        return ImageGenerationRequest(
            threadId: threadId,
            uploadedImages: uploadedImageInfos,
            parentId: nil,
            message: message,
            parameters: imageGenerationParameters,
            isFirst: isFirst
        )
    }

    private func createImageGenerationRetryRequest(message: String) -> ImageGenerationRequest {
        let threadId = threadManager.getCurrentThreadId()

        return ImageGenerationRequest(
            threadId: threadId,
            uploadedImages: uploadedImageInfos,
            parentId: threadId,
            message: message,
            parameters: imageGenerationParameters,
            isFirst: false
        )
    }

    // MARK: - 响应处理

    private func handleImageGenerationResponse(_ response: Res<ImageGenerationResponse>) {
        guard response.code == 200 else {
            handleError(BusinessError(code: response.code, message: response.msg))
            return
        }

        let imageData = response.data

        switch imageData {
        case .status(let statusResponse):
            handleImageGenerationStatus(statusResponse)

        case .imageGeneration(let contentResponse):
            handleImageGenerationContent(contentResponse.content)
        }
    }

    private func handleImageGenerationStatus(_ status: ImageGenerationStatusResponse) {
        print("ImageChatController: 图片生成状态 - event: \(status.event), node: \(status.node)")

        switch status.event {
        case "on_chain_start":
            print("ImageChatController: 图片生成链开始")
            // 创建占位符消息
            let placeholderImage = MessageImage(
                fileId: "placeholder",
                fileName: "generating.png",
                fileUrl: ""
            )

            let imageMessage = ChatMessageModel(
                content: "",
                isUser: false,
                type: .text,
                images: [placeholderImage],
                chatMode: .image
            )

            let newIndex = stableMessages.count
            stableMessages.append(imageMessage)
            messageManager.addMessageToCache(message: imageMessage, at: newIndex)

            // 记录占位符信息
            placeholderManager.setPlaceholder(messageId: imageMessage.id, at: newIndex)

        case "on_chain_end":
            print("ImageChatController: 图片生成链结束")
            // 清理占位符信息
            placeholderManager.reset()

        default:
            break
        }
    }

    private func handleImageGenerationContent(_ content: ImageGenerationContent) {
        if content.isPartialImage {
            // 处理部分图片数据（base64预览）
            if let index = content.partialImageIndex,
               let base64Data = content.imageB64,
               let placeholderInfo = placeholderManager.getPlaceholderInfo()
            {
                print("ImageChatController: 接收到部分图片数据，索引: \(index), base64长度: \(base64Data.count)")

                let mimeType = imageGenerationParameters.format.mimeType
                let dataUrl = "data:\(mimeType);base64,\(base64Data)"
                let previewImage = MessageImage(
                    fileId: "preview_\(index)",
                    fileName: "preview_\(index).\(imageGenerationParameters.format.rawValue)",
                    fileUrl: dataUrl
                )

                // 更新占位符消息
                if placeholderInfo.index < stableMessages.count {
                    let message = stableMessages[placeholderInfo.index]
                    stableMessages[placeholderInfo.index] = ChatMessageModel(
                        id: message.id,
                        content: message.content,
                        isUser: false,
                        type: .text,
                        images: [previewImage],
                        chatMode: .image
                    )
                }
            }

        } else if content.isCompleted {
            // 处理完成状态
            if let text = content.text, let imageUrl = content.imageUrl {
                print("ImageChatController: 图片生成完成，URL: \(imageUrl)")

                // 移除占位符消息
                if let placeholderInfo = placeholderManager.getPlaceholderInfo(),
                   placeholderInfo.index < stableMessages.count
                {
                    stableMessages.remove(at: placeholderInfo.index)
                    messageManager.clearCache() // 重建缓存
                    messageManager.updateIndexCache(for: stableMessages)
                }

                // 添加文本消息
                let textMessage = ChatMessageModel(
                    content: text,
                    isUser: false,
                    type: .text,
                    chatMode: .image
                )
                stableMessages.append(textMessage)
                messageManager.addMessageToCache(message: textMessage, at: stableMessages.count - 1)

                // 解析并创建图片消息
                let imageUrls = parseImageUrls(imageUrl)
                for (index, url) in imageUrls.enumerated() {
                    addImageMessage(imageUrl: url, index: index + 1)
                }
            }
        }
    }

    // 不再需要这个方法，因为我们直接使用data URL
    // func getImagePreviewData(for index: Int) -> String? {
    //     return imagePreviewData[index]
    // }

    private func addImageMessage(imageUrl: String, index: Int) {
        let messageImage = MessageImage(
            fileId: UUID().uuidString,
            fileName: "generated_image_\(index).png",
            fileUrl: imageUrl
        )

        let imageMessage = ChatMessageModel(
            content: "", // 图片消息不包含文字内容
            isUser: false,
            type: .text,
            images: [messageImage],
            chatMode: .image
        )

        let newIndex = stableMessages.count
        stableMessages.append(imageMessage)
        messageManager.addMessageToCache(message: imageMessage, at: newIndex)
    }

    private func parseImageUrls(_ imageUrl: String) -> [String] {
        // 如果包含逗号，按逗号分割
        if imageUrl.contains(",") {
            return imageUrl.components(separatedBy: ",")
                .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                .filter { !$0.isEmpty }
        } else {
            // 单个URL
            return [imageUrl]
        }
    }

    // MARK: - 重试状态管理

    private func setupRetryState(for message: ChatMessageModel) {
        isRetryingMessage = true
        retryingMessageId = message.id
        retryingVariantIndex = message.currentVariantIndex
        objectWillChange.send()
    }

    // MARK: - 完成处理

    private func handleStreamCompleted() {
        isLoading = false
        loadingType = .none
        generationState = .completed
        placeholderManager.reset()

        threadManager.continueCurrentSession()

        // 触发图片上传状态清理
        shouldClearUploadedImages = true

        if isRetryingMessage {
            finishRetryProcess()
        }
    }

    private func handleRetryCompleted() {
        if let retryingId = retryingMessageId,
           let lastMessage = stableMessages.last(where: { !$0.isUser })
        {
            finishRetryAsVariant(retryingId: retryingId, newMessage: lastMessage)
        }

        isLoading = false
        loadingType = .none
        generationState = .completed
        placeholderManager.reset()

        threadManager.continueCurrentSession()
        finishRetryProcess()
    }

    private func finishRetryProcess() {
        isRetryingMessage = false
        retryingMessageId = nil
        retryingVariantIndex = nil
        objectWillChange.send()
    }

    private func finishRetryAsVariant(retryingId: UUID, newMessage: ChatMessageModel) {
        guard let messageIndex = findMessageIndex(by: retryingId) else { return }

        let originalMessage = stableMessages[messageIndex]
        var allVariants = originalMessage.variants ?? []

        if allVariants.isEmpty {
            let originalVariant = MessageVariantModel(
                id: UUID().uuidString,
                content: originalMessage.content,
                modelId: nil,
                thinkingContent: originalMessage.reasoningContent,
                searchResults: originalMessage.searchResults,
                files: originalMessage.files,
                images: originalMessage.images
            )
            allVariants.append(originalVariant)
        }

        let newVariant = MessageVariantModel(
            id: UUID().uuidString,
            content: newMessage.content,
            modelId: nil,
            thinkingContent: newMessage.reasoningContent,
            searchResults: newMessage.searchResults,
            files: newMessage.files,
            images: newMessage.images
        )
        allVariants.append(newVariant)

        let currentVariantIndex = retryingVariantIndex ?? (allVariants.count - 1)
        let displayVariant = currentVariantIndex < allVariants.count ?
            allVariants[currentVariantIndex] : allVariants.last!

        let updatedMessage = ChatMessageModel(
            id: originalMessage.id,
            content: displayVariant.content,
            isUser: originalMessage.isUser,
            type: originalMessage.type,
            searchResults: displayVariant.searchResults,
            files: displayVariant.files,
            images: displayVariant.images,
            reasoningContent: displayVariant.thinkingContent,
            toolCallStatus: newMessage.toolCallStatus,
            isToolActive: newMessage.isToolActive,
            variants: allVariants,
            currentVariantIndex: currentVariantIndex,
            chatMode: originalMessage.chatMode
        )

        stableMessages[messageIndex] = updatedMessage
        messageManager.updateIndexCache(for: stableMessages)
    }

    // MARK: - 历史记录处理

    private func performLoadHistoryChat(threadId: String) async {
        do {
            let historyItems = try await chatService.getHistoryDetail(threadId: threadId, graphId: "chatimg_graph")

            await MainActor.run {
                let chatMessages = convertHistoryItemsToChatMessages(historyItems)

                self.stableMessages = chatMessages
                self.streamingMessage = nil

                // 一次性重建索引缓存
                self.messageManager.updateIndexCache(for: chatMessages)

                // 设置当前线程
                self.threadManager.setCurrentThread(threadId: threadId, isFirst: false)

                self.isLoading = false
                self.loadingType = .none
            }

        } catch {
            await MainActor.run {
                self.isLoading = false
                self.loadingType = .none

                let errorMsg = if let businessError = error as? BusinessError {
                    businessError.message
                } else {
                    error.localizedDescription
                }

                self.errorMessage = "加载历史对话失败: \(errorMsg)"
                self.showError = true
            }
        }
    }

    private func convertHistoryItemsToChatMessages(_ historyItems: [HistoryDetailItem]) -> [ChatMessageModel] {
        var chatMessages: [ChatMessageModel] = []

        for item in historyItems {
            let messages = item.toChatMessages(chatMode: .image)
            chatMessages.append(contentsOf: messages)
        }

        return chatMessages
    }

    // MARK: - 错误处理

    private func handleError(_ error: BusinessError) {
        isLoading = false
        loadingType = .none
        errorMessage = error.message
        showError = true

        // 清理状态
        generationState = .idle
        placeholderManager.reset()

        if isRetryingMessage {
            finishRetryProcess()
        }
    }
}

// MARK: - 占位符管理器

private class PlaceholderManager {
    private var placeholderMessageId: UUID?
    private var placeholderIndex: Int?

    func setPlaceholder(messageId: UUID, at index: Int) {
        placeholderMessageId = messageId
        placeholderIndex = index
    }

    func getPlaceholderInfo() -> (messageId: UUID, index: Int)? {
        guard let messageId = placeholderMessageId,
              let index = placeholderIndex
        else {
            return nil
        }
        return (messageId, index)
    }

    func reset() {
        placeholderMessageId = nil
        placeholderIndex = nil
    }
}

// MARK: - 流式内容管理器（复用）

private class StreamingContentManager: ObservableObject {
    private var contentBuffer = ""
    private var bufferTimer: Timer?
    private let bufferFlushInterval: TimeInterval = 0.05 // 50ms 缓冲
    private let maxBufferSize = 100 // 最大缓冲字符数

    func appendContent(_ content: String, completion: @escaping (String) -> Void) {
        contentBuffer += content

        // 达到缓冲阈值或定时器触发时刷新
        if contentBuffer.count >= maxBufferSize {
            flushBuffer(completion: completion)
        } else {
            scheduleBufferFlush(completion: completion)
        }
    }

    private func scheduleBufferFlush(completion: @escaping (String) -> Void) {
        bufferTimer?.invalidate()
        bufferTimer = Timer.scheduledTimer(withTimeInterval: bufferFlushInterval, repeats: false) { [weak self] _ in
            self?.flushBuffer(completion: completion)
        }
    }

    private func flushBuffer(completion: @escaping (String) -> Void) {
        guard !contentBuffer.isEmpty else { return }

        let content = contentBuffer
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil

        completion(content)
    }

    func reset() {
        contentBuffer = ""
        bufferTimer?.invalidate()
        bufferTimer = nil
    }

    func forceFlush(completion: @escaping (String) -> Void) {
        flushBuffer(completion: completion)
    }
}

// MARK: - 消息数据管理器（复用）

private class MessageDataManager {
    private var messageIndexCache: [UUID: Int] = [:]
    private var lastCacheSize = 0

    /// 增量更新索引缓存
    func updateIndexCache(for messages: [ChatMessageModel]) {
        let currentSize = messages.count

        if currentSize == lastCacheSize, !messageIndexCache.isEmpty {
            return
        }

        if currentSize > lastCacheSize, lastCacheSize > 0 {
            for index in lastCacheSize..<currentSize {
                if index < messages.count {
                    messageIndexCache[messages[index].id] = index
                }
            }
        } else {
            messageIndexCache.removeAll(keepingCapacity: true)
            for (index, message) in messages.enumerated() {
                messageIndexCache[message.id] = index
            }
        }

        lastCacheSize = currentSize
    }

    /// 添加单个消息到缓存
    func addMessageToCache(message: ChatMessageModel, at index: Int) {
        messageIndexCache[message.id] = index
        lastCacheSize = max(lastCacheSize, index + 1)
    }

    /// 清空缓存
    func clearCache() {
        messageIndexCache.removeAll(keepingCapacity: true)
        lastCacheSize = 0
    }

    func findMessageIndex(by id: UUID, in messages: [ChatMessageModel]) -> Int? {
        if let cachedIndex = messageIndexCache[id],
           cachedIndex < messages.count,
           messages[cachedIndex].id == id
        {
            return cachedIndex
        }

        if let index = messages.firstIndex(where: { $0.id == id }) {
            messageIndexCache[id] = index
            return index
        }

        return nil
    }
}
