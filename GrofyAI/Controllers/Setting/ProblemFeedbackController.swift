//
//  ProblemFeedbackController.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/22.
//

import Combine
import Foundation
import SwiftUI


enum ImageState {
    case local(UIImage)      // 刚选中，尚未上传
    case uploading(UIImage)  // 上传中，显示加载动画
    case uploaded(url: String, preview: UIImage) // 上传成功，使用Kingfisher加载，同时保留本地图用于预览
    case failed(UIImage)     // 上传失败，显示错误图标
    
    // 辅助属性，获取用于显示的底层UIImage
    var image: UIImage? {
        switch self {
        case .local(let img), .uploading(let img), .failed(let img):
            return img
        case .uploaded(_, let preview):
            return preview
        }
    }
    
    // 辅助属性，检查是否正在上传
    var isUploading: Bool {
        if case .uploading = self { return true }
        return false
    }
}

struct DisplayImage: Identifiable {
    let id: UUID = UUID() // 每张图片的唯一ID
    var state: ImageState
}

@MainActor
class ProblemFeedbackController: ObservableObject {
    
    // MARK: - Published 状态
    // 用于存储用户输入的联系邮箱
    @Published var contactEmail: String = ""
    // 用于存储用户输入的问题反馈内容
    @Published var feedbackText: String = ""
    // 用于存储用户上传后的图片地址
    @Published var displayImages: [DisplayImage] = []
    
    @Published var isSubmitting: Bool = false
    // 错误处理
    @Published var showError: Bool = false
    @Published var errorMessage: String? {
        didSet { showError = errorMessage != nil }
    }
    
    
    let fileService: FileService
    let problemFeedbackService: ProblemFeedbackService
    
    let maxImageCount = 5
    
    // MARK: - 初始化
    init(
        fileService: FileService = FileService(),
        problemFeedbackService: ProblemFeedbackService = ProblemFeedbackService()
    ) {
        self.fileService = fileService
        self.problemFeedbackService = problemFeedbackService
    }
    
    var isUploading: Bool {
        displayImages.contains { $0.state.isUploading }
    }
    
    var isDisabled: Bool {
        feedbackText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isUploading || isSubmitting
    }
    
    var canAddMoreImages: Bool {
        displayImages.count < maxImageCount
    }
    
    
    /// 当用户从相册选择图片后调用此方法
    func handleSelected(images: [UIImage]) {
        guard !images.isEmpty else { return }
        
        var newImagesToUpload: [(id: UUID, image: UIImage)] = []
        
        for image in images {
            // 为每张新图创建 'local' 状态的 DisplayImage 并添加到UI
            let displayImage = DisplayImage(state: .local(image))
            self.displayImages.append(displayImage)
            
            // 添加到临时列表，准备上传
            newImagesToUpload.append((id: displayImage.id, image: image))
        }
        
        uploadImages(imagesToUpload: newImagesToUpload)
    }
    // MARK: - 数据获取
    
    private func updateImageState(id: UUID, newState: ImageState) {
        if let index = displayImages.firstIndex(where: { $0.id == id }) {
            displayImages[index].state = newState
        }
    }
    /// 上传图片
    private func uploadImages(imagesToUpload: [(id: UUID, image: UIImage)]) {
        guard !imagesToUpload.isEmpty else { return }
        
        // 3. 在UI上，将这些特定的图片标记为 'uploading'
        for (id, image) in imagesToUpload {
            updateImageState(id: id, newState: .uploading(image))
        }
        
        Task {
            do {
                // 准备要发送给服务的数据。'name' 必须是UUID字符串。
                let imagesForService = imagesToUpload.map { (imageInfo: $0.image, name: $0.id.uuidString) }
                
                // 4. 调用服务
                // **注意**: 假设 fileService.uploadImages 现在返回 [UploadedImage]
                let uploadedResults = try await fileService.uploadImages(
                    images: imagesForService,
                    threadId: "0",
                    format: .png
                )
                
                print(uploadedResults)
                // 5. 处理上传结果
                var successfulIDs = Set<String>()
                for result in uploadedResults {
                    let imageName = result.originalName.replacingOccurrences(of: ".png", with: "")
                    print(imageName,displayImages)
                    // 通过 result.name (即我们的UUID字符串) 找到对应的图片
                    guard  URL(string: result.url) != nil , let id = UUID(uuidString: imageName) else { continue }
                    
                    
                    // 找到原始的UIImage用作预览图
                    if let originalImage = imagesToUpload.first(where: { $0.id == id })?.image {
                        updateImageState(id: id, newState: .uploaded(url: result.url, preview: originalImage))
                    }
                    successfulIDs.insert(imageName)
                }
                
                // 6. 将任何不在成功列表中的图片标记为 'failed'
                for (id, image) in imagesToUpload {
                    if !successfulIDs.contains(id.uuidString) {
                        updateImageState(id: id, newState: .failed(image))
                    }
                }
                
            } catch {
                // 如果整个请求失败，将这批次的所有图片都标记为失败，并显示错误信息
                for (id, image) in imagesToUpload {
                    updateImageState(id: id, newState: .failed(image))
                }
                handle(error: error)
            }
        }
    }
    
    /// 移除已上传的图片
    func removeImage(id: UUID) {
        displayImages.removeAll(where: { $0.id == id })
        // 在实际应用中，如果图片已上传，你可能还需要调用API从服务器上删除文件
    }
    
    /// 提交最终的反馈信息
    func onSubmit(completion: @escaping () -> Void) {
        guard !isSubmitting else { return }
        self.errorMessage = nil
        // 检查是否有图片正在上传
        guard !isUploading else {
            errorMessage = "请等待图片上传完成"
            return
        }

        // 【UX 改进】检查是否有上传失败的图片
        let hasFailedUploads = displayImages.contains { if case .failed = $0.state { return true } else { return false } }
        if hasFailedUploads {
            errorMessage = "有图片上传失败，请移除后重试"
            return
        }

        // 1. 设置提交状态
        isSubmitting = true
        
        // 提取所有成功上传的图片URL
        let finalImageUrls = displayImages.compactMap { displayImage -> String? in
            if case .uploaded(let url, _) = displayImage.state {
                return url
            }
            return nil
        }
        
        Task {
            // 2. 使用 defer 确保 isSubmitting 总能被重置，无论成功还是失败
            // defer 块中的代码会在 Task 作用域结束前执行
            defer {
                Task { @MainActor in
                    self.isSubmitting = false
                }
            }
            
            do {
                // 3. 将网络请求放在 do-catch 块中
                let req = ProblemFeedbackReq(
                    email: self.contactEmail,
                    content: self.feedbackText,
                    links: finalImageUrls
                )
                
                let res =  try await problemFeedbackService.problemFeedback(req: req)
                
                if res.code != 200 {
                    throw BusinessError(code: res.code, message: res.msg)
                }
                
                // 4. 成功后，回到主线程执行完成回调
                await MainActor.run {
                    completion()
                }
                
            } catch {
                // 5. 捕获任何错误，并在主线程上更新UI
                await MainActor.run {
                    self.handle(error: error) // 使用你已有的错误处理函数
                }
            }
        }
    }
    
    
    private func handle(error: Error) {
        if let networkError = error as? NetworkError {
            errorMessage = networkError.localizedDescription
        } else {
            errorMessage = error.localizedDescription
        }
    }
    
}
