import Foundation
import Kingfisher
import UIKit

class InitControl {
    static let shared = InitControl()

    func performCriticalSyncSetup() {
        setupTabBarAppearance()

        if AppConfig.Environment.current.isDevelopment {
            print(AppConfig.debugInfo)
        }
    }

    func performDelayedInitialization() async {
        try? await Task.sleep(nanoseconds: 100_000_000)

        await setupKingfisherCacheAsync()

        await performDelayedTokenRefresh()

        await initializeStoreKitDelayed()

        await loadModelListDelayed()
    }

    private func setupKingfisherCacheAsync() async {
        await MainActor.run {
            // 内存缓存
            ImageCache.default.memoryStorage.config.totalCostLimit = 100 * 1024 * 1024 // 100MB
            ImageCache.default.memoryStorage.config.countLimit = 200 // 最多缓存200张图片

            // 磁盘缓存
            ImageCache.default.diskStorage.config.sizeLimit = 200 * 1024 * 1024 // 200MB
            ImageCache.default.diskStorage.config.expiration = .days(30) // 30天

            ImageDownloader.default.downloadTimeout = 10.0 // 10秒
            ImageDownloader.default.sessionConfiguration.requestCachePolicy = .returnCacheDataElseLoad

            KingfisherManager.shared.defaultOptions.append(.backgroundDecode)
        }
    }

    /// 配置TabBar外观，确保TabBar有不透明的背景
    /// 支持浅色/深色模式自动适配
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()

        appearance.configureWithOpaqueBackground()

        appearance.backgroundColor = UIColor(named: "ColorBackgroundPage") ?? .systemBackground

        appearance.shadowColor = .clear

        let normalColor = UIColor(named: "ColorTextTertiary") ?? .systemGray
        let selectedColor = UIColor(named: "ColorBrandPrimary") ?? .systemBlue

        appearance.stackedLayoutAppearance.normal.iconColor = normalColor
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: normalColor,
        ]

        appearance.stackedLayoutAppearance.selected.iconColor = selectedColor
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: selectedColor,
        ]

        UITabBar.appearance().standardAppearance = appearance
        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
    }

    // MARK: - 异步任务

    private func performDelayedTokenRefresh() async {
        try? await Task.sleep(nanoseconds: 500_000_000)

        if AuthStore.shared.getAccessToken() != nil {
            await AuthStore.shared.refreshTokenOnAppLaunch()
        }
    }

    private func initializeStoreKitDelayed() async {
        try? await Task.sleep(nanoseconds: 1_000_000_000)

        await MainActor.run {
            _ = StoreKitService.shared
        }
    }

    private func loadModelListDelayed() async {
        try? await Task.sleep(nanoseconds: 1_500_000_000)

        await MainActor.run {
            ModelManager.shared.loadModelsInBackground()
        }
    }
}
