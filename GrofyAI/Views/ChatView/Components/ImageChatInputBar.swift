import PhotosUI
import SwiftUI
import UniformTypeIdentifiers

// MARK: - 图片对话输入框组件

struct ImageChatInputBar: View {
    @Binding var inputText: String
    @Binding var parameters: ImageGenerationParameters
    @Binding var uploadedImages: [String]
    @Binding var showParameters: Bool

    let shouldHideThumbnails: Bool
    let placeholder: String
    let onSend: (String) -> Void
    let onImageFileUpload: ((UploadImagesRes) -> Void)?
    let onImageRemove: ((String) -> Void)?
    let onMicrophoneAction: () -> Void
    let onParameterChange: (ImageGenerationParameters) -> Void
    let onStop: (() -> Void)?

    let isLoading: Bool
    var isInputFocused: FocusState<Bool>.Binding

    // 图片上传相关状态
    @State private var showMultiImagePicker = false
    @State private var uploadingCount = 0

    // FileService 实例
    private let fileService = FileService()

    // 线程ID
    let threadId: String

    init(
        inputText: Binding<String>,
        parameters: Binding<ImageGenerationParameters>,
        uploadedImages: Binding<[String]>,
        isInputFocused: FocusState<Bool>.Binding,
        threadId: String,
        showParameters: Binding<Bool>,
        shouldHideThumbnails: Bool = false,
        isLoading: Bool = false,
        placeholder: String = "描述您想要生成的图片...",
        onSend: @escaping (String) -> Void,
        onImageFileUpload: ((UploadImagesRes) -> Void)? = nil,
        onImageRemove: ((String) -> Void)? = nil,
        onMicrophoneAction: @escaping () -> Void = {},
        onParameterChange: @escaping (ImageGenerationParameters) -> Void = { _ in },
        onStop: (() -> Void)? = nil
    ) {
        _inputText = inputText
        _parameters = parameters
        _uploadedImages = uploadedImages
        _showParameters = showParameters
        self.shouldHideThumbnails = shouldHideThumbnails
        self.isInputFocused = isInputFocused
        self.threadId = threadId
        self.isLoading = isLoading
        self.placeholder = placeholder
        self.onSend = onSend
        self.onImageFileUpload = onImageFileUpload
        self.onImageRemove = onImageRemove
        self.onMicrophoneAction = onMicrophoneAction
        self.onParameterChange = onParameterChange
        self.onStop = onStop
    }

    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.xs)

            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
        .sheet(isPresented: $showMultiImagePicker) {
            MultiImagePicker { images in
                handleImagesSelected(images)
            }
        }
    }

    // MARK: - 输入框容器视图

    @ViewBuilder
    private var inputContainerView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            if !shouldHideThumbnails {
                UploadedImageThumbnailView(
                    uploadedImages: $uploadedImages,
                    onRemove: { imageUrl in
                        uploadedImages.removeAll { $0 == imageUrl }
                        onImageRemove?(imageUrl)
                    },
                    uploadingCount: uploadingCount
                )
            }

            optimizedTextInputArea

            optimizedBottomButtonRow
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.lg)
    }

    // MARK: - 参数配置区域

    @ViewBuilder
    private var parametersConfigurationView: some View {
        EmptyView()
    }

    // MARK: - 文本输入区域

    @ViewBuilder
    private var optimizedTextInputArea: some View {
        TextField(placeholder, text: $inputText, axis: .vertical)
            .fontLG(weight: DesignSystem.FontWeight.regular)
            .foregroundColor(DesignSystem.Colors.textPrimary)
            .focused(isInputFocused)
            .lineLimit(2...6)
            .textFieldStyle(.plain)
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.bottom, DesignSystem.Spacing.xs)
            .frame(minHeight: 44, alignment: .topLeading)
            .fixedSize(horizontal: false, vertical: true)
            .contentShape(Rectangle())
            .onTapGesture {
                handleTextFieldTap()
            }
            .accessibilityAddTraits(.isButton)
            .accessibilityLabel("消息输入框")
            .accessibilityHint("点击开始输入消息")
    }

    // MARK: - 底部按钮行

    @ViewBuilder
    private var optimizedBottomButtonRow: some View {
        HStack(spacing: 0) {
            optimizedLeftButtonGroup

            Spacer()

            optimizedRightButtonGroup
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }

    // MARK: - 左侧按钮组

    @ViewBuilder
    private var optimizedLeftButtonGroup: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Button(action: toggleParameters) {
                Image(systemName: "slider.horizontal.3")
                    .foregroundColor(showParameters ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
            }
            .buttonStyle(.plain)

            Button(action: handleImageUpload) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("编辑图片")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.horizontal, DesignSystem.Spacing.sm)
                .padding(.vertical, DesignSystem.Spacing.xs)
                .background(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                        .fill(Color.clear)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                        .stroke(DesignSystem.Colors.border, lineWidth: DesignSystem.BorderWidth.thin)
                )
            }
            .buttonStyle(.plain)

            InputButton(iconName: "IconInputMicrophone", action: onMicrophoneAction)
        }
    }

    // MARK: - 右侧按钮组

    @ViewBuilder
    private var optimizedRightButtonGroup: some View {
        HStack(spacing: 12) {
            if isLoading {
                Button(action: handleStopAction) {
                    Image(systemName: "stop.circle.fill")
                        .foregroundColor(.red)
                        .titleSmallStyle()
                }
                .buttonStyle(.plain)
            } else {
                SendButton(inputText: $inputText, action: handleSendAction)
            }
        }
    }

    // MARK: - 私有方法

    private func toggleParameters() {
        showParameters.toggle()
    }

    private func handleImageUpload() {
        showMultiImagePicker = true
    }

    /// 处理图片选择完成
    private func handleImagesSelected(_ images: [UIImage]) {
        Task {
            await uploadImages(images)
        }
    }

    /// 统一上传图片方法
    @MainActor
    private func uploadImages(_ images: [UIImage]) async {
        uploadingCount += images.count

        do {
            let imageData = images.enumerated().map { index, image in
                let imageName = "uploaded_image_\(Date().timeIntervalSince1970)_\(index).png"
                return (imageInfo: image, name: imageName)
            }

            let uploadResults = try await fileService.uploadImages(
                images: imageData,
                threadId: threadId,
                format: .png
            )

            var newImages = uploadedImages
            for result in uploadResults {
                newImages.append(result.url)

                onImageFileUpload?(result)

                print("✅ 图片上传成功: \(result.url)")
            }
            uploadedImages = newImages

        } catch {
            print("❌ 图片上传失败: \(error.localizedDescription)")
            ToastManager.shared.showError("图片上传失败，请重试")
        }

        uploadingCount -= images.count
    }

    private func handleSendAction() {
        let isSendEnabled = !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        guard isSendEnabled, !isLoading else { return }

        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        onSend(trimmedText)
        inputText = ""

        isInputFocused.wrappedValue = false
    }

    private func handleStopAction() {
        onStop?()
    }

    private func handleTextFieldTap() {
        guard !isInputFocused.wrappedValue else { return }

        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        isInputFocused.wrappedValue = true
    }
}

// MARK: - 多选图片选择器

struct MultiImagePicker: UIViewControllerRepresentable {
    var selectionLimit = 9
    let onImagesSelected: ([UIImage]) -> Void

    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.selectionLimit = selectionLimit
        configuration.filter = .images

        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: MultiImagePicker
        let maxFileSize = 10 * 1024 * 1024 // 10MB

        init(_ parent: MultiImagePicker) {
            self.parent = parent
        }

        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            picker.dismiss(animated: true)

            guard !results.isEmpty else { return }

            var images: [UIImage] = []
            var skippedCount = 0
            let group = DispatchGroup()

            for result in results {
                group.enter()

                // 先检查文件大小
                if result.itemProvider.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                    result.itemProvider
                        .loadFileRepresentation(forTypeIdentifier: UTType.image.identifier) { url, error in
                            defer { group.leave() }

                            guard let url else { return }

                            do {
                                let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
                                let fileSize = fileAttributes[.size] as? Int ?? 0

                                if fileSize > self.maxFileSize {
                                    DispatchQueue.main.async {
                                        skippedCount += 1
                                    }
                                    return
                                }

                                // 文件大小符合要求，加载图片
                                if let imageData = try? Data(contentsOf: url),
                                   let image = UIImage(data: imageData)
                                {
                                    DispatchQueue.main.async {
                                        images.append(image)
                                    }
                                }
                            } catch {
                                print("Error checking file size: \(error)")
                            }
                        }
                } else {
                    group.leave()
                }
            }

            group.notify(queue: .main) {
                if skippedCount > 0 {
                    let message = "已忽略 \(skippedCount) 张超过 10MB 的图片"
                    ToastManager.shared.showWarning(message)
                }
                if !images.isEmpty {
                    self.parent.onImagesSelected(images)
                }
            }
        }
    }
}
