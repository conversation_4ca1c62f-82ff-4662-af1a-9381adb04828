import FlowStacks
import MijickPopups
import SwiftUI

// MARK: - 应用首页主视图

struct ChatTabView: View {
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @Environment(\.verticalSizeClass) private var verticalSizeClass
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject var keyboardManager: KeyboardManager

    @StateObject private var chatSettingsManager = ChatSettingsManager()
    @ObservedObject private var personalFilesController = KnowledgeBaseFilesController.personalInstance
    @State private var showMorePressed = false
    @State private var inputText = ""
    @FocusState private var isInputFocused: Bool

    @State private var isKeyboardVisible = false

    // MARK: - 计算属性

    /// 最近上传的文件（前5个）
    private var recentFiles: [KnowledgeFile] {
        Array(personalFilesController.files.prefix(5))
    }

    /// 个人知识库文件加载状态
    private var isLoadingPersonalFiles: Bool {
        personalFilesController.isLoading
    }

    private let features: [FeatureCardItem] = [
        // AI对话
        FeatureCardItem(
            id: "textChat",
            feature: FeatureItem(
                id: "textChat",
                title: "AI对话",
                subtitle: "与多种AI模型\n智能对话交流",
                iconName: "message.fill",
                iconColor: DesignSystem.Colors.primary
            ),
            route: .textChat()
        ),
        // 图片生成
        FeatureCardItem(
            id: "imageGeneration",
            feature: FeatureItem(
                id: "imageGeneration",
                title: "图片生成",
                iconName: "photo.fill",
                iconColor: .purple
            ),
            route: .createImage(detail: nil)
        ),
        // 知识库
        FeatureCardItem(
            id: "knowledgeBase",
            feature: FeatureItem(
                id: "knowledgeBase",
                title: "知识库",
                iconName: "folder.fill",
                iconColor: .blue
            ),
            route: .personalKnowledgeBase
        ),
        // 视频生成
        FeatureCardItem(
            id: "videoGeneration",
            feature: FeatureItem(
                id: "videoGeneration",
                title: "视频生成",
                iconName: "video.fill",
                iconColor: .red
            ),
            route: .createVideo(detail: nil, createType: nil)
        ),
        // 图片识别
        FeatureCardItem(
            id: "imageRecognition",
            feature: FeatureItem(
                id: "imageRecognition",
                title: "图片识别",
                iconName: "camera.viewfinder",
                iconColor: .orange
            ),
            route: .imageRecognitionChat(threadId: nil)
        ),
    ]

    var body: some View {
        VStack(spacing: 0) {
            // 固定的导航栏
            ChatTabNavigationBar(
                onModelSelectionTap: showLLMModelSelectionPopup,
                onMenuTap: handleMenuTap
            )
            .zIndex(1001)

            // 主内容区域
            mainContentView
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .layoutPriority(1)

            // 输入栏
            ChatInputBar(
                inputText: $inputText,
                isInputFocused: $isInputFocused,
                enableThinking: $chatSettingsManager.enableThinking,
                enableNetworking: $chatSettingsManager.enableNetworking,
                onToggleThinking: {
                    chatSettingsManager.toggleThinking()
                },
                onToggleNetworking: {
                    chatSettingsManager.toggleNetworking()
                },
                onSend: handleSendMessage,
                onCameraAction: handleCameraAction,
                onVoiceAction: handleVoiceAction,
                onPlusAction: handlePlusAction
            )
            .layoutPriority(0)
        }
        .background(conditionalBackground)
        .navigationBarHidden(true)
        .onChange(of: keyboardManager.isVisible) { visible in
            isKeyboardVisible = visible
        }
    }

    // MARK: - 主内容视图

    @ViewBuilder
    private var mainContentView: some View {
        if isInputFocused {
            focusedContentView
        } else {
            normalContentView
        }
    }

    @ViewBuilder
    private var focusedContentView: some View {
        VStack(spacing: 20) {
            Spacer()
            BrandLogoView.largeIcon()
            Text("开启新会话")
                .popupTitleStyle()
                .foregroundColor(DesignSystem.Colors.textPrimary)
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .contentShape(Rectangle())
        .onTapGesture {
            isInputFocused = false
        }
        .animation(.easeOut(duration: 0.2), value: isInputFocused)
    }

    @ViewBuilder
    private var normalContentView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            ChatMainContent(
                features: features,
                personalFiles: recentFiles,
                isLoadingPersonalFiles: isLoadingPersonalFiles,
                showMorePressed: $showMorePressed,
                onFeatureTap: handleFeatureTap,
                onFileTap: handleFileTap,
                onViewMoreTap: handleViewMoreTap,
                onPersonalKnowledgeViewMoreTap: handlePersonalKnowledgeViewMoreTap,
                onMembershipTap: handleMembershipTap
            )
            .padding(.bottom, DesignSystem.Spacing.md)
        }
        .scrollDismissesKeyboard(.interactively)
        .animation(.easeOut(duration: 0.2), value: isInputFocused)
    }

    // MARK: - 条件背景视图

    @ViewBuilder
    private var conditionalBackground: some View {
        if isInputFocused {
            // 聚焦状态：简洁的纯色背景
            DesignSystem.Colors.backgroundPage
                .ignoresSafeArea()
        } else {
            // 正常状态：品牌背景图片（导航栏可穿透）
            backgroundView
        }
    }

    // MARK: - 背景视图

    @ViewBuilder
    private var backgroundView: some View {
        Group {
            if colorScheme == .light {
                Image("ImageBrandBackground")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                DesignSystem.Colors.backgroundPage
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - ChatTabView 扩展方法

extension ChatTabView {
    private func showLLMModelSelectionPopup() {
        Task {
            await LLMModelSelectionPopup(
                onModelSelected: { model in
                    print("ChatTabView: 选择模型: \(model.safeName)")
                }
            )
            .present()
        }
    }

    // MARK: - 导航栏事件处理

    private func handleMenuTap() {
        navigator.push(Route.chatHistory(chatMode: .agent))
    }

    // MARK: - 内容区域事件处理

    private func handleFeatureTap(_ feature: FeatureCardItem) {
        switch feature.route {
        case .textChat:
            navigator.push(Route.textChat(initialMessage: nil, threadId: nil))
        case .imageRecognitionChat:
            navigator.push(Route.imageRecognitionChat(threadId: nil))
        default:
            navigator.push(feature.route)
        }
    }

    private func handleFileTap(_ file: KnowledgeFile) {
        navigator.push(Route.personalKnowledgeBase)
    }

    private func handleViewMoreTap() {
        // TODO: 实现查看更多功能
        print("查看更多被点击")
    }

    private func handlePersonalKnowledgeViewMoreTap() {
        navigator.push(Route.personalKnowledgeBase)
    }

    private func handleMembershipTap() {
        navigator.push(Route.payment)
    }

    private func handleSendMessage(_ message: String) {
        inputText = ""
        isInputFocused = false

        navigator.push(Route.textChat(initialMessage: message, threadId: nil))
    }

    private func handleCameraAction() {
        // TODO: 实现相机功能
        print("相机按钮被点击")
    }

    private func handleVoiceAction() {
        // TODO: 实现语音通话功能
        print("语音通话按钮被点击")
    }

    private func handlePlusAction() {
        Task {
            await FeatureSelectionPopup(
                onFeatureSelected: { feature in
                    navigator.push(feature.route)
                }
            )
            .present()
        }
    }
}
