import SwiftUI

// MARK: - 最近活动列表组件

struct RecentActivityList: View {
    let files: [KnowledgeFile]
    let isLoading: Bool
    let onFileTap: (KnowledgeFile) -> Void
    let onViewMoreTap: () -> Void

    init(
        files: [KnowledgeFile] = [],
        isLoading: Bool = false,
        onFileTap: @escaping (KnowledgeFile) -> Void = { _ in },
        onViewMoreTap: @escaping () -> Void = {}
    ) {
        self.files = files
        self.isLoading = isLoading
        self.onFileTap = onFileTap
        self.onViewMoreTap = onViewMoreTap
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            headerSection

            if isLoading, files.isEmpty {
                loadingContentView
            } else if files.isEmpty {
                emptyStateContent
            } else {
                fileListContent
            }
        }
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
        .animation(.easeInOut(duration: 0.3), value: isLoading)
        .animation(.easeInOut(duration: 0.3), value: files.isEmpty)
    }

    // MARK: - 标题栏

    private var headerSection: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.xs) {
            Text("最近上传")
                .cardTitleStyle()

            Spacer()

            Button(action: onViewMoreTap) {
                HStack(spacing: DesignSystem.Spacing.xs) {
                    Text("查看更多")
                        .descriptionTextStyle()

                    Image(systemName: "chevron.right")
                        .fontXS(weight: DesignSystem.FontWeight.medium)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .buttonStyle(.plain)
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.top, DesignSystem.Spacing.md)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }

    // MARK: - 加载状态视图（骨架屏）

    @ViewBuilder
    private var loadingContentView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 0) {
                ForEach(0..<3, id: \.self) { _ in
                    RecentActivitySkeletonCard()
                }
            }
        }
        .frame(height: 160)
        .padding(.bottom, DesignSystem.Spacing.sm)
        .redacted(reason: .placeholder)
    }

    // MARK: - 文件列表内容

    private var fileListContent: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 0) {
                ForEach(files, id: \.id) { file in
                    RecentActivityCard(
                        file: file,
                        onTap: {
                            onFileTap(file)
                        }
                    )
                }
            }
        }
        .frame(height: 160)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }

    // 空状态内容
    private var emptyStateContent: some View {
        EmptyStateView(
            iconName: "book",
            title: "暂无文件",
            iconColor: .orange,
            style: .compact
        )
        .frame(height: 160)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }
}

// MARK: - 最近活动卡片

struct RecentActivityCard: View {
    let file: KnowledgeFile
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                let (iconName, iconColor) = file.typeIcon
                Image(systemName: iconName)
                    .fontXXL()
                    .foregroundColor(iconColor)
                    .frame(width: 40, height: 40)
                    .background(iconColor.opacity(0.1))
                    .cornerRadius(DesignSystem.Rounded.md)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(file.title ?? "未知文件")
                        .fontMD(weight: DesignSystem.FontWeight.regular)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)

                    HStack(spacing: DesignSystem.Spacing.sm) {
                        HStack(spacing: 2) {
                            Image(systemName: file.statusIcon)
                                .font(.system(size: 8, weight: .medium))
                                .foregroundColor(file.statusColor)

                            Text(file.statusText)
                                .fontXS(weight: DesignSystem.FontWeight.medium)
                                .foregroundColor(file.statusColor)
                        }

                        Text("•")
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                        Text(file.formattedCreateDate)
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary)

                        Text("•")
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                        Text(file.formattedSize)
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary)

                        tagCountIndicator(for: file)

                        Spacer()
                    }
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .fontSM(weight: DesignSystem.FontWeight.medium)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            .padding(.horizontal, DesignSystem.Spacing.md)
            .padding(.vertical, DesignSystem.Spacing.xs)
        }
        .buttonStyle(.plain)
    }

    // MARK: - 标签数量指示器

    @ViewBuilder
    private func tagCountIndicator(for file: KnowledgeFile) -> some View {
        if let tags = file.tags, !tags.isEmpty {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Text("•")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                Image(systemName: "tag")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary)

                Text("\(tags.count)")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
        }
    }
}

// MARK: - 最近活动骨架屏卡片

struct RecentActivitySkeletonCard: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            // 文件图标骨架屏
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                .fill(DesignSystem.Colors.border.opacity(0.3))
                .frame(width: 40, height: 40)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                // 文件名骨架屏
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(height: 16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .scaleEffect(x: 0.7, anchor: .leading)

                // 状态信息骨架屏
                HStack(spacing: DesignSystem.Spacing.sm) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 50, height: 12)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 60, height: 12)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 40, height: 12)
                }
            }

            Spacer()

            // 箭头图标骨架屏
            RoundedRectangle(cornerRadius: 2)
                .fill(DesignSystem.Colors.border.opacity(0.3))
                .frame(width: 12, height: 12)
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.vertical, DesignSystem.Spacing.xs)
    }
}
