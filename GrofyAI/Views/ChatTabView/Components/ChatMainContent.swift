import SwiftUI

// MARK: - 聊天页面主要内容区域组件

struct ChatMainContent: View {
    let features: [FeatureCardItem]
    let personalFiles: [KnowledgeFile]
    let isLoadingPersonalFiles: Bool
    @Binding var showMorePressed: Bool

    let onFeatureTap: (FeatureCardItem) -> Void
    let onFileTap: (KnowledgeFile) -> Void
    let onViewMoreTap: () -> Void
    let onPersonalKnowledgeViewMoreTap: () -> Void
    let onMembershipTap: () -> Void

    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            ChatBrandSection()

            VStack(spacing: DesignSystem.Spacing.sm) {
                ChatToolsHeader(
                    showMorePressed: $showMorePressed,
                    onViewMoreTap: onViewMoreTap
                )

                FeatureCardsLayout(
                    features: features,
                    onFeatureTap: onFeatureTap
                )
            }

            MembershipPromoBanner(onUpgradeAction: onMembershipTap)

            RecentActivityList(
                files: personalFiles,
                isLoading: isLoadingPersonalFiles,
                onFileTap: onFileTap,
                onViewMoreTap: onPersonalKnowledgeViewMoreTap
            )
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .transition(.opacity.combined(with: .scale(scale: 0.98)))
    }
}

// MARK: - 品牌Logo区域组件

private struct ChatBrandSection: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            BrandLogoView.largeIcon()

            Text(AppConfig.App.displayName)
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color(hex: "7CDFFF"), Color(hex: "B48FFF")],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .titleLargeStyle()
                .fontWeight(DesignSystem.FontWeight.bold)
                .italic()
                .multilineTextAlignment(.center)
                .lineLimit(nil)

            Text("您的 AI 工作站，让创作更简单")
                .subtitleStyle()
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, DesignSystem.Spacing.sm)
    }
}

// MARK: - 热门工具标题栏组件

private struct ChatToolsHeader: View {
    @Binding var showMorePressed: Bool
    let onViewMoreTap: () -> Void

    var body: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.sm) {
            Text("热门工具")
                .cardTitleStyle()

            Spacer()

            Button(action: onViewMoreTap) {
                HStack(spacing: 4) {
                    Text("查看更多")
                        .descriptionTextStyle()

                    Image(systemName: "chevron.right")
                        .fontXS(weight: DesignSystem.FontWeight.medium)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .buttonStyle(.plain)
            .scaleEffect(showMorePressed ? 0.95 : 1.0)
            .opacity(showMorePressed ? 0.7 : 1.0)
            .onLongPressGesture(
                minimumDuration: 0,
                maximumDistance: .infinity,
                pressing: { pressing in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        showMorePressed = pressing
                    }
                },
                perform: {}
            )
        }
    }
}
