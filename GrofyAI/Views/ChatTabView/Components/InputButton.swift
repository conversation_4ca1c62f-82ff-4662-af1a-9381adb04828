import SwiftUI

/// 输入框按钮组件
struct InputButton: View {
    let iconName: String
    let action: () -> Void
    let isEnabled: Bool
    let isSystemIcon: Bool

    @State private var cachedForegroundColor: Color

    init(iconName: String, isEnabled: Bool = true, isSystemIcon: Bool = false, action: @escaping () -> Void) {
        self.iconName = iconName
        self.isEnabled = isEnabled
        self.isSystemIcon = isSystemIcon
        self.action = action
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors
            .textSecondary.opacity(0.4)
        )
    }

    init(systemIcon: String, isEnabled: Bool = true, action: @escaping () -> Void) {
        iconName = systemIcon
        self.isEnabled = isEnabled
        isSystemIcon = true
        self.action = action
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors
            .textSecondary.opacity(0.4)
        )
    }

    var body: some View {
        Button(action: action) {
            Group {
                if isSystemIcon {
                    Image(systemName: iconName)
                } else {
                    Image(iconName)
                }
            }
            .foregroundColor(cachedForegroundColor)
            .font(.system(size: DesignSystem.FontSize.lg, weight: DesignSystem.FontWeight.medium))
        }
        .buttonStyle(.plain)
        .disabled(!isEnabled)
        .onChange(of: isEnabled) { enabled in
            cachedForegroundColor = enabled ? DesignSystem.Colors.textSecondary : DesignSystem.Colors.textSecondary
                .opacity(0.4)
        }
    }
}

// MARK: - 切换按钮组件

struct ToggleInputButton: View {
    let iconName: String
    let title: String
    @Binding var isSelected: Bool
    let action: () -> Void

    @State private var cachedBackgroundColor: Color
    @State private var cachedForegroundColor: Color
    @State private var cachedBorderColor: Color

    init(iconName: String, title: String, isSelected: Binding<Bool>, action: @escaping () -> Void) {
        self.iconName = iconName
        self.title = title
        _isSelected = isSelected
        self.action = action

        let selected = isSelected.wrappedValue
        _cachedBackgroundColor = State(initialValue: selected ? DesignSystem.Colors.primary : Color.clear)
        _cachedForegroundColor = State(initialValue: selected ? .white : DesignSystem.Colors.textSecondary)
        _cachedBorderColor = State(initialValue: DesignSystem.Colors.border)
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(iconName)
                    .foregroundColor(cachedForegroundColor)

                Text(title)
                    .fontSM(weight: DesignSystem.FontWeight.medium)
                    .foregroundColor(cachedForegroundColor)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .fill(cachedBackgroundColor)
            )
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(cachedBorderColor, lineWidth: DesignSystem.BorderWidth.thin)
            )
        }
        .buttonStyle(.plain)
        .onChange(of: isSelected) { selected in
            cachedBackgroundColor = selected ? DesignSystem.Colors.primary : Color.clear
            cachedForegroundColor = selected ? .white : DesignSystem.Colors.textSecondary
        }
    }
}

// MARK: - 发送按钮组件

struct SendButton: View {
    let action: () -> Void
    @Binding var inputText: String

    @State private var cachedIsEnabled = false
    @State private var cachedForegroundColor: Color
    @State private var cachedOpacity = 0.4

    init(inputText: Binding<String>, action: @escaping () -> Void) {
        _inputText = inputText
        self.action = action

        let isEnabled = !inputText.wrappedValue.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        _cachedIsEnabled = State(initialValue: isEnabled)
        _cachedForegroundColor = State(initialValue: isEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors
            .textSecondary
        )
        _cachedOpacity = State(initialValue: isEnabled ? 1.0 : 0.4)
    }

    var body: some View {
        Button(action: action) {
            Image("IconInputSend")
                .foregroundColor(cachedForegroundColor)
        }
        .buttonStyle(.plain)
        .disabled(!cachedIsEnabled)
        .opacity(cachedOpacity)
        .onChange(of: inputText) { text in
            let newIsEnabled = !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
            if newIsEnabled != cachedIsEnabled {
                cachedIsEnabled = newIsEnabled
                cachedForegroundColor = newIsEnabled ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary
                cachedOpacity = newIsEnabled ? 1.0 : 0.4
            }
        }
    }
}
