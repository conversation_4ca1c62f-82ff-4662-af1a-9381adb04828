import FlowStacks
import SwiftUI

struct KnowledgeBaseFilesView: View {
    @StateObject private var viewModel: KnowledgeBaseFilesController
    @StateObject private var uploadManager = FileUploadManager()
    @EnvironmentObject var navigator: FlowPathNavigator
    @State private var showDeleteAlert = false
    @State private var showBatchDeleteAlert = false
    @State private var showFilePicker = false
    @State private var selectedFiles: [SelectedFile] = []
    @State private var tagUpdateTasks: [String: Task<Void, Never>] = [:]

    // 配置参数
    private let showUploadButton: Bool
    private let navigationTitle: String

    init(categoryId: Int, showUploadButton: Bool = true, navigationTitle: String = "知识库文件") {
        _viewModel = StateObject(wrappedValue: KnowledgeBaseFilesController(categoryId: categoryId))
        self.showUploadButton = showUploadButton
        self.navigationTitle = navigationTitle
    }

    var body: some View {
        VStack(spacing: 0) {
            fileListContent

            // 整体上传进度（当有文件正在上传时显示）
            if uploadManager.isUploading {
                overallUploadProgress
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
            }

            if !viewModel.isSelectionMode, showUploadButton {
                uploadButton
                    .disabled(uploadManager.isUploading)
                    .opacity(uploadManager.isUploading ? 0.6 : 1.0)
            }
        }
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                toolbarLeadingItem()
            }
            ToolbarItem(placement: .topBarTrailing) {
                toolbarTrailingItem()
            }
            ToolbarItem(placement: .principal) {
                toolbarPrincipalItem()
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .onAppear {
            if viewModel.files.isEmpty, !viewModel.isLoading {
                viewModel.loadFiles(loadType: .initial)
            }
        }
        .onDisappear {
            viewModel.resetViewState()
            ToastManager.shared.clearToast()

            // 清理所有标签更新任务
            for task in tagUpdateTasks.values {
                task.cancel()
            }
            tagUpdateTasks.removeAll()
        }
        .sheet(isPresented: $showFilePicker) {
            DocumentFilePicker(
                selectedFiles: $selectedFiles,
                allowMultipleSelection: true
            )
        }
        .onChange(of: selectedFiles) { files in
            if !files.isEmpty {
                // 添加文件到上传管理器
                uploadManager.addFiles(files)
                selectedFiles.removeAll()

                // 开始上传
                if let categoryId = viewModel.categoryId {
                    uploadManager.startUpload(categoryId: categoryId)
                }
            }
        }
        .onChange(of: uploadManager.isUploading) { isUploading in
            if !isUploading, uploadManager.hasSuccessfulUploads {
                handleUploadCompletion()
            }
        }
        .nativeStyleRefreshable {
            try await viewModel.performFilesRefresh()
        }
        .alert("确认删除", isPresented: $showBatchDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                viewModel.deleteSelectedFiles()
            }
        } message: {
            Text("确定要删除选中的 \(viewModel.selectedItems.count) 个文件吗？此操作无法撤销。")
        }
        .sheet(isPresented: $viewModel.showCategorySelection) {
            if let fileToMove = viewModel.fileToMove {
                KnowledgeCategorySelectionView(
                    fileToMove: fileToMove,
                    currentCategoryId: viewModel.categoryId,
                    sourceCategory: viewModel.sourceCategory,
                    onCategorySelected: { targetCategory in
                        viewModel.moveFile(fileToMove, to: targetCategory)
                    },
                    onCancel: {
                        viewModel.cancelMoveFile()
                    }
                )
                .environmentObject(viewModel)
            }
        }
        .requireAuthenticationWithNavigation("请先登录以访问知识库文件") {
            navigator.pop()
        }
    }

    // MARK: - Toolbar Items

    @ViewBuilder
    private func toolbarLeadingItem() -> some View {
        BackButton(onBack: handleBackTap)
    }

    @ViewBuilder
    private func toolbarTrailingItem() -> some View {
        if viewModel.isSelectionMode {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if !viewModel.selectedItems.isEmpty {
                    Button(action: {
                        showBatchDeleteAlert = true
                    }) {
                        HStack(spacing: DesignSystem.Spacing.xs) {
                            if viewModel.isDeletingFiles {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.error))
                                    .scaleEffect(0.8)
                                    .frame(width: 16, height: 16)
                            } else {
                                Text("删除")
                                    .font(DesignSystem.Typography.body)
                                    .foregroundColor(DesignSystem.Colors.error)
                            }
                        }
                    }
                    .buttonStyle(.plain)
                    .disabled(viewModel.isDeletingFiles)
                    .animation(.easeInOut(duration: 0.2), value: viewModel.isDeletingFiles)
                }

                Button(action: viewModel.toggleSelectAll) {
                    Text(viewModel.selectedItems.count == viewModel.files.count ? "取消全选" : "全选")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(viewModel.isDeleteInProgress ?
                            DesignSystem.Colors.disabled : DesignSystem.Colors.primary
                        )
                }
                .buttonStyle(.plain)
                .disabled(viewModel.isDeleteInProgress)

                Button(action: viewModel.toggleSelectionMode) {
                    Text("取消")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(viewModel.isDeleteInProgress ?
                            DesignSystem.Colors.disabled : DesignSystem.Colors.textSecondary
                        )
                }
                .buttonStyle(.plain)
                .disabled(viewModel.isDeleteInProgress)
            }
        } else if !viewModel.files.isEmpty, !viewModel.isLoading {
            Button(action: {
                viewModel.toggleSelectionMode()
            }) {
                Text("选择")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.primary)
            }
            .buttonStyle(.plain)
        }
    }

    @ViewBuilder
    private func toolbarPrincipalItem() -> some View {
        if viewModel.isSelectionMode {
            Text("已选择 \(viewModel.selectedItems.count)/\(viewModel.files.count) 项")
                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
        } else {
            Text(navigationTitle)
                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }

    // MARK: - 处理返回按钮点击

    private func handleBackTap() {
        if viewModel.isSelectionMode {
            viewModel.toggleSelectionMode()
        } else {
            navigator.pop()
        }
    }

    // MARK: - 处理文件点击

    private func handleFileTap(_ file: KnowledgeFile) {
        // 检查文件状态是否就绪
        guard file.isReady, file.isVectorReady else {
            ToastManager.shared.showError("文件正在处理中，请稍后再试")
            return
        }

        // 跳转到文件对话界面
        navigator.push(Route.fileChat(
            knowledgeId: file.id,
            fileName: file.title,
            initialMessage: nil,
            threadId: file.threadId
        ))
    }

    // MARK: - 文件列表内容

    @ViewBuilder
    private var fileListContent: some View {
        if viewModel.shouldShowErrorState {
            errorStateView
        } else if viewModel.shouldShowEmptyState {
            emptyStateView
        } else if viewModel.isLoading, viewModel.files.isEmpty {
            loadingContentView
        } else {
            fileListView
        }
    }

    // MARK: - 加载内容视图（骨架屏）

    @ViewBuilder
    private var loadingContentView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(0..<4, id: \.self) { _ in
                    SkeletonFileRowView()
                }
            }
            .padding()
        }
        .redacted(reason: .placeholder)
    }

    // MARK: - 文件列表视图

    @ViewBuilder
    private var fileListView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                // 显示已存在的文件
                ForEach(viewModel.files) { file in
                    KnowledgeFileRowView(
                        file: file,
                        isSelectionMode: viewModel.isSelectionMode,
                        isSelected: viewModel.selectedItems.contains(file.id),
                        onTap: {
                            if viewModel.isSelectionMode {
                                viewModel.toggleSelection(for: file)
                            } else {
                                handleFileTap(file)
                            }
                        },
                        onTagsUpdated: { updatedTags in
                            let taskKey = "tag_update_\(file.id)"
                            tagUpdateTasks[taskKey]?.cancel()

                            tagUpdateTasks[taskKey] = Task {
                                do {
                                    try await viewModel.updateFileTags(fileId: file.id, tags: updatedTags)
                                    DispatchQueue.main.async {
                                        ToastManager.shared.showSuccess("更新成功")
                                    }
                                } catch {
                                    if !Task.isCancelled {
                                        DispatchQueue.main.async {
                                            ToastManager.shared.showError("更新失败")
                                        }
                                    }
                                }
                                DispatchQueue.main.async {
                                    tagUpdateTasks.removeValue(forKey: taskKey)
                                }
                            }
                        },
                        onMoveFile: { fileToMove in
                            Task {
                                await viewModel.startMoveFile(fileToMove)
                            }
                        }
                    )
                }

                if viewModel.isLoadingMore {
                    HStack(spacing: DesignSystem.Spacing.sm) {
                        ProgressView()
                            .scaleEffect(0.8)

                        Text("加载更多...")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    .padding(.vertical, DesignSystem.Spacing.lg)
                } else if viewModel.canLoadMore, !viewModel.files.isEmpty {
                    Color.clear
                        .frame(height: 80)
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                viewModel.loadFiles(loadType: .loadMore)
                            }
                        }
                } else if !viewModel.canLoadMore, !viewModel.files.isEmpty {
                    Text("没有更多文件了 (共\(viewModel.fileCount)个)")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                        .padding(.vertical, DesignSystem.Spacing.lg)
                }
            }
            .padding()
        }
    }

    // MARK: - 空状态视图

    @ViewBuilder
    private var emptyStateView: some View {
        EmptyStateView(
            iconName: "doc.badge.plus",
            title: "暂无文件",
            description: showUploadButton ? "点击下方按钮上传您的第一个文件" : "暂时没有文件",
            style: .fullScreen
        )
    }

    // MARK: - 错误状态视图

    @ViewBuilder
    private var errorStateView: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            Spacer()

            VStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 64))
                    .foregroundColor(DesignSystem.Colors.warning)

                Text("加载失败")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .font(DesignSystem.Typography.subheadline)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }

                Button("重试") {
                    viewModel.clearError()
                    Task {
                        await viewModel.refreshFiles()
                    }
                }
                .buttonStyle(.borderedProminent)
            }

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // 上传按钮
    private var uploadButton: some View {
        Button(action: {
            showFilePicker = true
        }) {
            HStack {
                Image(systemName: "arrow.up.circle.fill")
                Text("上传文件")
            }
            .font(DesignSystem.Typography.headline)
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(DesignSystem.Colors.primary)
            .cornerRadius(DesignSystem.Rounded.lg)
        }
        .padding()
    }

    // MARK: - 整体上传进度（集成文件列表）

    private var overallUploadProgress: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            uploadProgressHeader

            if !uploadManager.uploadItems.isEmpty {
                uploadFilesList
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.primary.opacity(0.05))
        .cornerRadius(DesignSystem.Rounded.md)
        .padding(.horizontal, DesignSystem.Spacing.lg)
    }

    // 进度条头部
    private var uploadProgressHeader: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            HStack {
                Image(systemName: "arrow.up.circle")
                    .foregroundColor(DesignSystem.Colors.primary)

                Text(uploadStatusText)
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 进度百分比
                Text("\(Int(uploadManager.uploadProgress * 100))%")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.primary)

                // 文件数量
                let stats = uploadManager.uploadStats
                Text("\(stats.success + stats.uploading)/\(stats.total)")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }

            ProgressView(value: uploadManager.uploadProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: DesignSystem.Colors.primary))
        }
    }

    // 上传状态文本
    private var uploadStatusText: String {
        let stats = uploadManager.uploadStats
        let progress = uploadManager.uploadProgress

        if progress >= 0.88, progress < 1.0 {
            return "正在处理文件..."
        } else if stats.uploading > 0 {
            return "正在上传 \(stats.uploading) 个文件..."
        } else {
            return "正在上传文件..."
        }
    }

    // 上传文件列表
    private var uploadFilesList: some View {
        VStack(spacing: 0) {
            Divider()
                .background(DesignSystem.Colors.primary.opacity(0.2))

            Spacer()
                .frame(height: DesignSystem.Spacing.sm)

            let maxVisibleFiles = 3
            let totalFiles = uploadManager.uploadItems.count

            if totalFiles <= maxVisibleFiles {
                LazyVStack(spacing: DesignSystem.Spacing.xs) {
                    ForEach(uploadManager.uploadItems) { uploadItem in
                        compactUploadFileRow(uploadItem)
                    }
                }
            } else {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    ScrollView {
                        LazyVStack(spacing: DesignSystem.Spacing.xs) {
                            ForEach(uploadManager.uploadItems) { uploadItem in
                                compactUploadFileRow(uploadItem)
                            }
                        }
                    }
                    .frame(maxHeight: CGFloat(maxVisibleFiles) * 45)

                    // 滚动提示
                    if totalFiles > maxVisibleFiles {
                        HStack {
                            Spacer()
                            Text("共 \(totalFiles) 个文件，可滚动查看")
                                .fontXS()
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                            Spacer()
                        }
                        .padding(.top, DesignSystem.Spacing.xs)
                    }
                }
            }
        }
    }

    // 紧凑的上传文件行
    private func compactUploadFileRow(_ uploadItem: UploadFileItem) -> some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            let (iconName, iconColor) = uploadItem.file.typeIcon
            Image(systemName: iconName)
                .fontMD()
                .foregroundColor(iconColor)
                .frame(width: 20, height: 20)

            Text(uploadItem.file.name)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1)
                .truncationMode(.middle)

            Spacer()

            uploadStatusIndicator(uploadItem.status)
        }
        .padding(.horizontal, DesignSystem.Spacing.sm)
        .padding(.vertical, DesignSystem.Spacing.xs)
        .background(DesignSystem.Colors.backgroundCard.opacity(0.5))
        .cornerRadius(DesignSystem.Rounded.sm)
    }

    // 状态指示器
    private func uploadStatusIndicator(_ status: FileUploadStatus) -> some View {
        HStack(spacing: DesignSystem.Spacing.xs) {
            switch status {
            case .pending:
                Image(systemName: "clock")
                    .fontSM()
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                Text("等待")
                    .font(.system(size: 11))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

            case .uploading:
                ProgressView()
                    .scaleEffect(0.6)
                Text("上传中")
                    .font(.system(size: 11))
                    .foregroundColor(DesignSystem.Colors.primary)

            case .success:
                Image(systemName: "checkmark.circle.fill")
                    .fontSM()
                    .foregroundColor(DesignSystem.Colors.success)
                Text("完成")
                    .font(.system(size: 11))
                    .foregroundColor(DesignSystem.Colors.success)

            case .failed:
                Image(systemName: "exclamationmark.circle.fill")
                    .fontSM()
                    .foregroundColor(DesignSystem.Colors.error)
                Text("失败")
                    .font(.system(size: 11))
                    .foregroundColor(DesignSystem.Colors.error)
            }
        }
    }

    // MARK: - 上传完成处理

    /// 处理上传完成后的状态
    private func handleUploadCompletion() {
        Task {
            do {
                try await viewModel.performFilesRefresh()
                viewModel.startFileStatusPolling()
            } catch {
                viewModel.startFileStatusPolling()

                if let businessError = error as? BusinessError {
                    ToastManager.shared.showError("刷新失败: \(businessError.message)")
                } else {
                    ToastManager.shared.showError("刷新失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - 骨架屏文件行视图

struct SkeletonFileRowView: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(DesignSystem.Colors.backgroundCard)
                .frame(width: 40, height: 40)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.backgroundCard)
                    .frame(height: 16)
                    .frame(maxWidth: .infinity, alignment: .leading)

                HStack(spacing: DesignSystem.Spacing.sm) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 80, height: 12)

                    Circle()
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 4, height: 4)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 50, height: 12)

                    Circle()
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 4, height: 4)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .frame(width: 60, height: 12)

                    Spacer()
                }
            }

            Spacer()
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
    }
}
