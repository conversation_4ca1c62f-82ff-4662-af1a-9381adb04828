import SwiftUI

// MARK: - 知识库分类行视图

struct KnowledgeCategoryRowView: View {
    let category: KnowledgeCategory
    let onTap: () -> Void
    let onEdit: (() -> Void)?
    let onDelete: (() -> Void)?

    init(
        category: KnowledgeCategory,
        onTap: @escaping () -> Void,
        onEdit: (() -> Void)? = nil,
        onDelete: (() -> Void)? = nil
    ) {
        self.category = category
        self.onTap = onTap
        self.onEdit = onEdit
        self.onDelete = onDelete
    }

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            categoryIcon

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(category.title)
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                if category.content != nil {
                    Text(category.contentSummary)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)
        }
        .contentShape(Rectangle())
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
        .onTapGesture(perform: onTap)
        .contextMenu {
            contextMenuContent
        }
        .accessibilityAddTraits(.isButton)
    }

    // MARK: - contextMenu内容

    @ViewBuilder
    private var contextMenuContent: some View {
        Group {
            if let onEdit {
                Button(action: onEdit) {
                    HStack {
                        Image(systemName: "pencil")
                        Text("编辑")
                    }
                }
            }

            if let onDelete {
                Button(role: .destructive, action: onDelete) {
                    HStack {
                        Image(systemName: "trash")
                        Text("删除")
                    }
                }
            }
        }
    }

    // MARK: - 分类图标

    @ViewBuilder
    private var categoryIcon: some View {
        if category.hasIcon {
            Image(systemName: category.displayIconName)
                .font(.title2)
                .foregroundColor(category.displayIconColor)
                .frame(width: 40, height: 40)
                .background(category.displayIconColor.opacity(0.1))
                .cornerRadius(DesignSystem.Rounded.sm)
        } else {
            Image(systemName: category.displayIconName)
                .font(.title2)
                .foregroundColor(category.displayIconColor)
                .frame(width: 40, height: 40)
                .background(category.displayIconColor.opacity(0.1))
                .cornerRadius(DesignSystem.Rounded.sm)
        }
    }
}
