
import SwiftUI

struct KnowledgeFileRowView: View {
    let file: KnowledgeFile
    let isSelectionMode: Bool
    let isSelected: Bool
    let onTap: () -> Void
    let onTagsUpdated: (([String]) -> Void)?
    let onMoveFile: ((KnowledgeFile) -> Void)?

    @State private var showTagEditView = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.md) {
                fileTypeIcon
                    .frame(width: 40, height: 40)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(file.title ?? "")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)

                    HStack(spacing: DesignSystem.Spacing.sm) {
                        HStack(spacing: 3) {
                            Image(systemName: file.statusIcon)
                                .font(.system(size: 9, weight: .medium))
                                .foregroundColor(file.statusColor)

                            Text(file.statusText)
                                .fontXS(weight: DesignSystem.FontWeight.medium)
                                .foregroundColor(file.statusColor)
                        }

                        Text("•")
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                        Text(file.formattedCreateDate)
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary)

                        Text("•")
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                        Text(file.formattedSize)
                            .fontXS(weight: DesignSystem.FontWeight.regular)
                            .foregroundColor(DesignSystem.Colors.textTertiary)

                        tagCountIndicator

                        Spacer()
                    }

                    tagDisplayView
                }

                if isSelectionMode {
                    selectionIndicator
                }
            }
            .padding(DesignSystem.Spacing.md)
            .background(isSelected ? DesignSystem.Colors.primary.opacity(0.1) : DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
        }
        .buttonStyle(.plain)
        .accessibilityAddTraits(.isButton)
        .contextMenu {
            contextMenuContent
        }
        .sheet(isPresented: $showTagEditView) {
            TagEditView(
                file: file,
                isPresented: $showTagEditView,
                onTagsUpdated: { updatedTags in
                    // 通知父组件更新
                    onTagsUpdated?(updatedTags)
                }
            )
        }
    }

    // MARK: - 选择指示器

    @ViewBuilder
    private var selectionIndicator: some View {
        ZStack {
            if isSelected {
                Circle()
                    .fill(DesignSystem.Colors.primary)
                    .frame(width: 24, height: 24)

                Image(systemName: "checkmark")
                    .fontSM(weight: DesignSystem.FontWeight.bold)
                    .foregroundColor(.white)
            } else {
                Circle()
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(width: 24, height: 24)
            }
        }
    }

    // MARK: - 文件类型图标

    @ViewBuilder
    private var fileTypeIcon: some View {
        let (iconName, iconColor) = file.typeIcon
        Image(systemName: iconName)
            .font(.title2)
            .foregroundColor(iconColor)
            .frame(width: 40, height: 40)
            .background(iconColor.opacity(0.1))
            .cornerRadius(DesignSystem.Rounded.sm)
    }

    // MARK: - 标签数量指示器

    @ViewBuilder
    private var tagCountIndicator: some View {
        if let tags = file.tags, !tags.isEmpty {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Text("•")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary.opacity(0.6))

                Image(systemName: "tag")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary)

                Text("\(tags.count)")
                    .fontXS(weight: DesignSystem.FontWeight.regular)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
        }
    }

    // MARK: - 标签显示视图

    @ViewBuilder
    private var tagDisplayView: some View {
        if let tags = file.tags, !tags.isEmpty {
            HStack(spacing: DesignSystem.Spacing.xs) {
                editTagButton

                tagScrollView(tags: tags)
            }
        } else {
            addTagButton
        }
    }

    @ViewBuilder
    private func tagScrollView(tags: [String]) -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                ForEach(tags, id: \.self) { tag in
                    HStack(spacing: DesignSystem.Spacing.xs) {
                        Image(systemName: "tag")
                            .fontXS()
                        Text(tag)
                            .font(DesignSystem.Typography.cardSubtitle)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.sm)
                    .padding(.vertical, DesignSystem.Spacing.xs)
                    .background(DesignSystem.Colors.backgroundInput)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .cornerRadius(DesignSystem.Rounded.sm)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                            .stroke(
                                DesignSystem.Colors.separator.opacity(0.5),
                                lineWidth: DesignSystem.BorderWidth.thin
                            )
                    )
                }
            }
        }
    }

    @ViewBuilder
    private var addTagButton: some View {
        Button(action: {
            showTagEditView = true
        }) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(systemName: "tag")
                    .fontXS()
                Text("添加标签")
                    .font(DesignSystem.Typography.cardSubtitle)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(DesignSystem.Colors.backgroundCard)
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .cornerRadius(DesignSystem.Rounded.sm)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(DesignSystem.Colors.separator, lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }

    @ViewBuilder
    private var editTagButton: some View {
        Button(action: {
            showTagEditView = true
        }) {
            HStack(spacing: DesignSystem.Spacing.xs) {
                Image(systemName: "pencil")
                    .fontXS()
                Text("编辑标签")
                    .font(DesignSystem.Typography.cardSubtitle)
            }
            .padding(.horizontal, DesignSystem.Spacing.sm)
            .padding(.vertical, DesignSystem.Spacing.xs)
            .background(DesignSystem.Colors.backgroundCard)
            .foregroundColor(DesignSystem.Colors.textSecondary)
            .cornerRadius(DesignSystem.Rounded.sm)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                    .stroke(DesignSystem.Colors.separator, lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.1), value: false)
    }

    // MARK: - 长按菜单内容

    @ViewBuilder
    private var contextMenuContent: some View {
        Group {
            // 编辑标签选项
            Button(action: {
                showTagEditView = true
            }) {
                HStack {
                    Image(systemName: "tag")
                    Text("编辑标签")
                }
            }

            // 移动文件选项
            if let onMoveFile {
                Button(action: {
                    onMoveFile(file)
                }) {
                    HStack {
                        Image(systemName: "folder")
                        Text("移动至其他知识库")
                    }
                }
            }
        }
    }
}
