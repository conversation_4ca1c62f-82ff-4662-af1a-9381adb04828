import FlowStacks
import SwiftUI

struct KnowledgeTabView: View {
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject var viewModel: KnowledgeTabController
    @EnvironmentObject var authStore: AuthStore

    @State private var showDeleteAlert = false
    @State private var categoryToDelete: KnowledgeCategory?
    @State private var hasInitializedData = false
    @State private var refreshTask: Task<Void, Never>?

    private let personalKnowledgeBase = FeatureItem(
        id: "personal",
        title: "个人知识库",
        subtitle: "管理您的个人文件和文档",
        iconName: "person.fill",
        iconColor: DesignSystem.Colors.primary
    )

    var body: some View {
        ScrollView {
            VStack(spacing: DesignSystem.Spacing.lg) {
                personalKnowledgeSection

                Divider()
                    .background(DesignSystem.Colors.separator)

                knowledgeCategoriesSection
            }
            .padding()
        }
        .navigationTitle("知识库")
        .background(DesignSystem.Colors.backgroundPage)
        .nativeStyleRefreshable {
            try await viewModel.performCategoriesRefresh()
        }
        .onAppear {
            loadCategoriesWhenReady()
        }
        .onDisappear {
            ToastManager.shared.clearToast()
            refreshTask?.cancel()
            refreshTask = nil
        }
        .onReceive(NotificationCenter.default.publisher(for: .userAuthenticationReady)) { _ in
            loadCategoriesWhenReady()
        }
        .onReceive(NotificationCenter.default.publisher(for: .knowledgeCategoryUpdated)) { _ in
            refreshTask?.cancel()

            refreshTask = Task {
                try? await viewModel.performCategoriesRefresh()
            }
        }
        .onChange(of: authStore.user.accessToken) { token in
            if token != nil, !hasInitializedData {
                loadCategoriesWhenReady()
            }
        }
        .animation(.easeInOut(duration: 0.25), value: viewModel.isLoading)
        .animation(.easeInOut(duration: 0.25), value: viewModel.categories.count)
        .alert("删除知识库", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) {
                categoryToDelete = nil
            }
            Button("删除", role: .destructive) {
                if let category = categoryToDelete {
                    deleteCategory(category)
                }
            }
        } message: {
            if let category = categoryToDelete {
                Text("确定要删除知识库「\(category.title)」吗？此操作不可撤销。")
            }
        }
        .requireAuthenticationWithPlaceholder("请先登录以访问知识库")
    }

    // MARK: - 个人知识库区域

    @ViewBuilder
    private var personalKnowledgeSection: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            Image(systemName: personalKnowledgeBase.iconName)
                .font(.title2)
                .foregroundColor(personalKnowledgeBase.iconColor)
                .frame(width: 40, height: 40)
                .background(personalKnowledgeBase.iconColor.opacity(0.1))
                .cornerRadius(DesignSystem.Rounded.sm)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(personalKnowledgeBase.title)
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)

                Text(personalKnowledgeBase.subtitle ?? "")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }

            Spacer()

            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)
        }
        .contentShape(Rectangle())
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
        .onTapGesture {
            navigator.push(Route.personalKnowledgeBase)
        }
        .accessibilityAddTraits(.isButton)
        .accessibilityLabel("个人知识库")
        .accessibilityHint("点击进入个人知识库管理")
    }

    // MARK: - 知识库分类列表区域

    @ViewBuilder
    private var knowledgeCategoriesSection: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            HStack {
                Text("知识库广场")
                    .font(DesignSystem.Typography.headline)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            createKnowledgeBaseButton

            // 内容区域
            if viewModel.isLoading, viewModel.categories.isEmpty {
                // 初始加载状态：显示骨架屏
                loadingContentView
            } else if viewModel.isLoading, !viewModel.categories.isEmpty {
                // 刷新状态：显示骨架屏而不是现有数据
                loadingContentView
            } else if viewModel.categories.isEmpty, !viewModel.isLoading {
                // 空状态：确认不在加载且没有数据
                emptyStateView
            } else {
                // 有数据状态：显示分类列表
                LazyVStack(spacing: DesignSystem.Spacing.sm) {
                    ForEach(viewModel.categories) { category in
                        KnowledgeCategoryRowView(
                            category: category,
                            onTap: {
                                navigator.push(Route.knowledgeBaseFiles(
                                    categoryId: category.id,
                                    categoryName: category.title
                                ))
                            },
                            onEdit: {
                                editCategory(category)
                            },
                            onDelete: {
                                confirmDeleteCategory(category)
                            }
                        )
                    }

                    if !viewModel.categories.isEmpty, !viewModel.isLoading {
                        Text("没有更多知识库了 (共\(viewModel.categories.count)个)")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .padding(.vertical, DesignSystem.Spacing.lg)
                    }
                }
            }
        }
    }

    // MARK: - 加载内容视图（骨架屏）

    @ViewBuilder
    private var loadingContentView: some View {
        VStack(spacing: DesignSystem.Spacing.sm) {
            ForEach(0..<3, id: \.self) { _ in
                SkeletonCategoryRowView()
            }
        }
        .redacted(reason: .placeholder)
    }

    // MARK: - 空状态视图

    @ViewBuilder
    private var emptyStateView: some View {
        EmptyStateView(
            iconName: "folder.badge.questionmark",
            title: "暂无知识库",
            description: "开始新建您的第一个知识库吧"
        )
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
    }

    // MARK: - 新建知识库按钮

    @ViewBuilder
    private var createKnowledgeBaseButton: some View {
        Button(action: {
            navigator.push(Route.createKnowledgeCategory)
        }) {
            HStack(spacing: DesignSystem.Spacing.md) {
                Image(systemName: "plus.circle.fill")
                    .font(.title2)
                    .foregroundColor(DesignSystem.Colors.primary)
                    .frame(width: 40, height: 40)
                    .background(DesignSystem.Colors.primary.opacity(0.1))
                    .cornerRadius(DesignSystem.Rounded.sm)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text("新建知识库")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .lineLimit(1)

                    Text("创建新的知识库")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(1)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.md)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.Rounded.md)
                    .stroke(DesignSystem.Colors.primary.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 数据加载方法

    /// 在token可用时加载知识库分类数据
    private func loadCategoriesWhenReady() {
        guard authStore.getAccessToken() != nil else {
            return
        }

        guard !hasInitializedData else {
            return
        }

        hasInitializedData = true

        Task {
            await viewModel.loadKnowledgeCategoriesIfNeeded()
        }
    }

    // MARK: - 编辑和删除方法

    /// 编辑分类
    private func editCategory(_ category: KnowledgeCategory) {
        navigator.push(Route.editKnowledgeCategory(categoryId: category.id))
    }

    /// 确认删除分类
    private func confirmDeleteCategory(_ category: KnowledgeCategory) {
        categoryToDelete = category
        showDeleteAlert = true
    }

    /// 删除分类
    private func deleteCategory(_ category: KnowledgeCategory) {
        Task {
            do {
                try await viewModel.deleteCategories(ids: [category.id])

                await MainActor.run {
                    ToastManager.shared.showSuccess("删除成功")
                    categoryToDelete = nil

                    NotificationCenter.default.post(name: .knowledgeCategoryUpdated, object: nil)
                }

            } catch {
                await MainActor.run {
                    let errorMessage: String = if let businessError = error as? BusinessError {
                        businessError.message
                    } else if let networkError = error as? NetworkError {
                        networkError.localizedDescription
                    } else {
                        error.localizedDescription
                    }

                    ToastManager.shared.showError("删除失败: \(errorMessage)")
                    categoryToDelete = nil
                }
            }
        }
    }
}

// MARK: - 骨架屏组件

private struct SkeletonCategoryRowView: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 模拟图标
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(DesignSystem.Colors.backgroundCard)
                .frame(width: 40, height: 40)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                // 模拟标题
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.backgroundCard)
                    .frame(height: 16)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 模拟描述
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.backgroundCard)
                    .frame(height: 12)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .scaleEffect(x: 0.7, anchor: .leading)
            }

            Spacer()

            // 模拟右箭头
            RoundedRectangle(cornerRadius: 2)
                .fill(DesignSystem.Colors.backgroundCard)
                .frame(width: 8, height: 14)
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundCard)
        .cornerRadius(DesignSystem.Rounded.md)
    }
}
