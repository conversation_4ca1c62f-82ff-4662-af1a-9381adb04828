import FlowStacks
import SwiftUI

struct ImageRecognitionChat: View {
    @EnvironmentObject var controller: ImageRecognitionChatController
    @EnvironmentObject var navigator: FlowPathNavigator
    @EnvironmentObject var ttsService: TextToSpeechService
    @EnvironmentObject var sttService: SpeechRecognitionService
    @State private var keyboardHeight: CGFloat = 0
    @FocusState private var isInputFocused: Bool
    @State private var showSpeechInput = false

    var threadId: String?

    init(threadId: String? = nil) {
        self.threadId = threadId
    }

    var body: some View {
        GeometryReader { _ in
            VStack(spacing: 0) {
                ImageRecognitionChatNavigationBar(
                    onBackTap: { navigator.popToRoot() }
                )

                ChatList(
                    messages: controller.messages,
                    isLoading: controller.isLoading,
                    loadingText: getLoadingText(for: controller.loadingType),
                    showSkeleton: shouldShowSkeleton(for: controller.loadingType),

                    onRetryMessage: { messageId in
                        controller.retryMessage(messageId: messageId)
                    },
                    onCopyMessage: { message in
                        controller.copyMessage(message)
                    },
                    onShareMessage: { message in
                        controller.shareMessage(message)
                    },
                    imageRecognition: true
//                    onVariantChanged: { messageId, variantIndex in
//                        controller.switchMessageVariant(messageId: messageId, variantIndex: variantIndex)
//                    },
//                    getVariantInfo: { messageId in
//                        controller.getMessageVariantInfo(messageId: messageId)
//                    }
                )
                .frame(maxWidth: .infinity)
                .layoutPriority(1)
                .contentShape(Rectangle())
                .onTapGesture {
                    if isInputFocused {
                        isInputFocused = false
                    }
                }
                .animation(.easeOut(duration: 0.15), value: isInputFocused)
                .animation(.easeInOut(duration: 0.2), value: keyboardHeight)

                ImageRecognitionInput(
                    inputText: $controller.inputText,
                    isInputFocused: $isInputFocused,
                    isLoading: controller.isLoading,
                    placeholder: "请输入...",
                    onSend: { _ in
                        controller.sendMessage()
                    },
                    onCameraAction: {
                        print("相机功能待实现")
                    },
                    onMicrophoneAction: {
                        showSpeechInput = true
                    },
                    onStop: {
                        controller.stopStreaming()
                    }
                )
                .layoutPriority(0)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.backgroundPage)
        .navigationBarHidden(true)
        .onAppear {
            // 当视图出现时，命令 Controller 开始上传图片
            if threadId == nil {
                controller.uploadInitialImages()
            } else {
                controller.loadHistoryChat(threadId: threadId ?? "")
            }
        }
        .onDisappear {
            if !controller.handlePickedIamges.isEmpty {
                controller.clearAllState()
                ToastManager.shared.clearToast()
            }

            Task {
                await ttsService.cleanup()
                sttService.cleanup()
            }
        }
    }

    private func getLoadingText(for loadingType: ChatLoadingType) -> String {
        switch loadingType {
        case .streaming:
            return "正在分析图片..."
        case .loadingHistory:
            return "正在加载历史对话..."
        case .none:
            return ""
        default:
            return ""
        }
    }

    private func shouldShowSkeleton(for loadingType: ChatLoadingType) -> Bool {
        switch loadingType {
        case .loadingHistory:
            return true
        case .none, .streaming, .uploading:
            return false
        }
    }
}

private struct ImageRecognitionChatNavigationBar: View {
    let onBackTap: () -> Void
    @EnvironmentObject var navigator: FlowPathNavigator

    var body: some View {
        HStack(alignment: .center, spacing: DesignSystem.Spacing.lg) {
            Button(action: onBackTap) {
                Image(systemName: "chevron.backward")
                    .font(.system(size: 18))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .buttonStyle(.plain)

            VStack(alignment: .leading, spacing: 2) {
                Text("图片识别")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
            }

            Spacer()

            Image(systemName: "clock")
                .foregroundStyle(DesignSystem.Colors.textPrimary)
                .font(.title3)
                .onTapGesture {
                    navigator.push(Route.imageRecognitionHistory)
                }
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.backgroundPage)
        .overlay(
            Rectangle()
                .fill(DesignSystem.Colors.border)
                .frame(height: 0.5),
            alignment: .bottom
        )
    }
}
