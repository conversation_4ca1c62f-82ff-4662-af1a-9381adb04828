//
//  ImageRecognitionInput.swift
//  GrofyAI
//
//  Created by kissy on 2025/7/10.
//
import SwiftUI

struct ImageRecognitionInput: View {
    // MARK: - Bindings & Properties
    @Binding var inputText: String
    let placeholder: String
    let onSend: (String) -> Void
    let onCameraAction: () -> Void
    let onMicrophoneAction: () -> Void
    let onStop: (() -> Void)?
    let isLoading: Bool
    var isInputFocused: FocusState<Bool>.Binding

    // MARK: - Initializer
    init(
        inputText: Binding<String>,
        isInputFocused: FocusState<Bool>.Binding,
        isLoading: Bool = false,
        placeholder: String = "询问任何问题",
        onSend: @escaping (String) -> Void,
        onCameraAction: @escaping () -> Void = {},
        onMicrophoneAction: @escaping () -> Void = {},
        onStop: (() -> Void)? = nil
    ) {
        _inputText = inputText
        self.isInputFocused = isInputFocused
        self.isLoading = isLoading
        self.placeholder = placeholder
        self.onSend = onSend
        self.onCameraAction = onCameraAction
        self.onMicrophoneAction = onMicrophoneAction
        self.onStop = onStop
    }

    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            inputContainerView
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.top, DesignSystem.Spacing.sm)
                .padding(.bottom, DesignSystem.Spacing.xs)

            // Safe area bottom padding
            Rectangle()
                .fill(DesignSystem.Colors.backgroundInput)
                .frame(height: 0)
                .safeAreaInset(edge: .bottom) {
                    Rectangle()
                        .fill(DesignSystem.Colors.backgroundInput)
                        .frame(height: 8)
                }
        }
        .background(DesignSystem.Colors.backgroundInput)
        .fixedSize(horizontal: false, vertical: true)
    }

    // MARK: - 可重用组件

    /// 输入框容器视图
    private var inputContainerView: some View {
        compactLayoutView
            .background(DesignSystem.Colors.backgroundCard)
            .cornerRadius(DesignSystem.Rounded.lg)
    }

    /// 紧凑布局视图
    private var compactLayoutView: some View {
        VStack(spacing: DesignSystem.Spacing.xs) {
            TextField(placeholder, text: $inputText, axis: .vertical)
                .fontLG(weight: DesignSystem.FontWeight.regular)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .focused(isInputFocused)
                .lineLimit(1...6)
                .textFieldStyle(.plain)
                .padding( DesignSystem.Spacing.lg)
                .padding(.bottom,0)
                .frame(minHeight: 44, alignment: .topLeading)

            bottomButtonRow
        }
    }
    
    
    /// 底部按钮行
    @ViewBuilder
    private var bottomButtonRow: some View {
        HStack(spacing: 0) {
            HStack(spacing: DesignSystem.Spacing.md) {
                InputButton(iconName: "IconInputMicrophone", action: onMicrophoneAction)
//                InputButton(iconName: "IconInputCamera", action: onCameraAction)
                ImageRecognitionIconView()
            }

            Spacer()

            rightButtonGroup
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }


    /// 右侧发送/停止按钮组
    @ViewBuilder
    private var rightButtonGroup: some View {
        // isLoading 为 true 时显示停止按钮，否则显示发送按钮
        if isLoading {
            Button(action: handleStopAction) {
                Image(systemName: "stop.circle.fill")
                    .foregroundColor(.red)
                    .titleSmallStyle() // 假设这是一个自定义的样式
            }
            .buttonStyle(.plain)
        } else {
            SendButton(inputText: $inputText, action: handleSendAction)
        }
    }

    // MARK: - 事件处理

    /// 处理发送消息
    private func handleSendAction() {
        let trimmedText = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        // 只有在文本不为空且不在加载中时才发送
        guard !trimmedText.isEmpty, !isLoading else { return }

        onSend(trimmedText)
        inputText = ""
    }

    /// 处理停止流式响应
    private func handleStopAction() {
        onStop?()
    }
}
