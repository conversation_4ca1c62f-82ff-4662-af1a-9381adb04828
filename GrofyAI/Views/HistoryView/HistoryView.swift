import FlowStacks
import SwiftUI

// MARK: - 历史记录主视图

struct HistoryView: View {
    let chatMode: ChatMode?

    @ObservedObject private var historyController = HistoryController.shared
    @EnvironmentObject var navigator: FlowPathNavigator
    @ObservedObject private var modelManager = ModelManager.shared
    @State private var showDeleteAlert = false
    @State private var showBatchDeleteAlert = false
    @State private var itemToDelete: HistoryItem?

    init(chatMode: ChatMode? = .agent) {
        self.chatMode = chatMode
    }

    var body: some View {
        VStack(spacing: 0) {
            HistoryListView(
                historyGroups: historyController.historyGroups,
                isLoading: historyController.isLoading,
                isLoadingMore: historyController.isLoadingMore,
                hasMoreData: historyController.hasMoreData,
                isSelectionMode: historyController.isSelectionMode,
                selectedItems: historyController.selectedItems,
                availableModels: modelManager.availableModels,
                onItemTap: { item in
                    if historyController.isSelectionMode {
                        historyController.toggleSelection(for: item)
                    } else {
                        historyController.enterChat(item: item)
                    }
                },
                onItemDelete: { item in
                    itemToDelete = item
                    showDeleteAlert = true
                },
                onLoadMore: historyController.loadMoreHistory
            )
        }
        .navigationBarBackButtonHidden()
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .topBarLeading) {
                toolbarLeadingItem()
            }
            ToolbarItem(placement: .topBarTrailing) {
                toolbarTrailingItem()
            }
            ToolbarItem(placement: .principal) {
                toolbarPrincipalItem()
            }
        }
        .hideNavigationBarLine(backgroundColor: DesignSystem.Colors.backgroundPage)
        .background(DesignSystem.Colors.backgroundPage)
        .nativeStyleRefreshable {
            try await historyController.performRefreshOperation()
        }
        .onAppear {
            historyController.setFilterChatMode(chatMode)

            historyController.onEnterChat = { [weak navigator] threadId in
                guard let navigator else { return }

                switch chatMode {
                case .agent, .none:
                    navigator.push(Route.textChat(initialMessage: nil, threadId: threadId))
                case .image:
                    navigator.pop()

                    NotificationCenter.default.post(
                        name: .imageChatSwitchToHistory,
                        object: nil,
                        userInfo: [
                            "threadId": threadId,
                        ]
                    )
                case .rag:
                    let knowledgeId = historyController.getKnowledgeId(for: threadId) ?? 0

                    navigator.pop()

                    NotificationCenter.default.post(
                        name: .fileChatSwitchToHistory,
                        object: nil,
                        userInfo: [
                            "knowledgeId": knowledgeId,
                            "threadId": threadId,
                            "fileName": "历史对话",
                        ]
                    )
                case .vision:
                    // 图像识别聊天跳转到imageRecognitionChat，后续可能需要支持threadId
                    navigator.push(Route.imageRecognitionChat(threadId: threadId))
                }
            }

            Task { await historyController.loadHistoryIfNeeded() }

            NotificationCenter.default.post(name: .requestModelListLoad, object: nil)
        }
        .onDisappear {
            historyController.resetViewState()
            ToastManager.shared.clearToast()
        }
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                if let item = itemToDelete {
                    historyController.deleteHistory(item: item)
                }
            }
        } message: {
            Text("确定要删除这条历史记录吗？此操作无法撤销。")
        }
        .alert("批量删除", isPresented: $showBatchDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                historyController.deleteSelectedHistories()
            }
        } message: {
            Text("确定要删除选中的 \(historyController.selectedItems.count) 条历史记录吗？此操作无法撤销。")
        }
        .requireAuthenticationWithNavigation("请先登录以查看历史记录") {
            navigator.goBack()
        }
    }

    // MARK: - Toolbar Items

    @ViewBuilder
    private func toolbarLeadingItem() -> some View {
        BackButton(onBack: handleBackTap)
    }

    @ViewBuilder
    private func toolbarTrailingItem() -> some View {
        if historyController.isSelectionMode {
            HStack(spacing: DesignSystem.Spacing.sm) {
                if !historyController.selectedItems.isEmpty {
                    Button(action: {
                        showBatchDeleteAlert = true
                    }) {
                        HStack(spacing: DesignSystem.Spacing.xs) {
                            if historyController.isDeletingBatch {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: DesignSystem.Colors.error))
                                    .scaleEffect(0.8)
                                    .frame(width: 16, height: 16)
                            } else {
                                Text("删除")
                                    .font(DesignSystem.Typography.body)
                                    .foregroundColor(DesignSystem.Colors.error)
                            }
                        }
                    }
                    .buttonStyle(.plain)
                    .disabled(historyController.isDeletingBatch)
                    .animation(.easeInOut(duration: 0.2), value: historyController.isDeletingBatch)
                }

                Button(action: historyController.toggleSelectAll) {
                    Text(historyController.selectedItems.count == historyController.historyGroups.flatMap(\.items)
                        .count ? "取消全选" : "全选"
                    )
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(historyController.isAnyDeleteInProgress ?
                        DesignSystem.Colors.disabled : DesignSystem.Colors.primary
                    )
                }
                .buttonStyle(.plain)
                .disabled(historyController.isAnyDeleteInProgress)

                Button(action: historyController.toggleSelectionMode) {
                    Text("取消")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(historyController.isAnyDeleteInProgress ?
                            DesignSystem.Colors.disabled : DesignSystem.Colors.textSecondary
                        )
                }
                .buttonStyle(.plain)
                .disabled(historyController.isAnyDeleteInProgress)
            }
        } else if !historyController.historyGroups.isEmpty, !historyController.isLoading {
            TextButton(text: "选择") {
                historyController.toggleSelectionMode()
            }
        }
    }

    @ViewBuilder
    private func toolbarPrincipalItem() -> some View {
        if historyController.isSelectionMode {
            Text(
                "已选择 \(historyController.selectedItems.count)/\(historyController.historyGroups.flatMap(\.items).count) 项"
            )
            .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
            .foregroundColor(DesignSystem.Colors.textPrimary)
        } else {
            Text("历史记录")
                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
        }
    }

    private func handleBackTap() {
        navigator.goBack()
    }
}

// MARK: - 历史记录列表视图

private struct HistoryListView: View {
    let historyGroups: [(date: String, items: [HistoryItem])]
    let isLoading: Bool
    let isLoadingMore: Bool
    let hasMoreData: Bool
    let isSelectionMode: Bool
    let selectedItems: Set<Int>
    let availableModels: [LLMRes]
    let onItemTap: (HistoryItem) -> Void
    let onItemDelete: (HistoryItem) -> Void
    let onLoadMore: () -> Void

    var body: some View {
        if isLoading {
            loadingContentView
        } else if historyGroups.isEmpty {
            HistoryEmptyView()
        } else {
            ScrollView {
                LazyVStack(spacing: DesignSystem.Spacing.lg) {
                    ForEach(historyGroups, id: \.date) { group in
                        HistoryGroupView(
                            date: group.date,
                            items: group.items,
                            isSelectionMode: isSelectionMode,
                            selectedItems: selectedItems,
                            availableModels: availableModels,
                            onItemTap: onItemTap,
                            onItemDelete: onItemDelete
                        )
                    }

                    if isLoadingMore {
                        HStack(spacing: DesignSystem.Spacing.sm) {
                            ProgressView()
                                .scaleEffect(0.8)

                            Text("加载更多...")
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        .padding(.vertical, DesignSystem.Spacing.lg)
                    } else if hasMoreData {
                        Color.clear
                            .frame(height: 80)
                            .onAppear {
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    onLoadMore()
                                }
                            }
                    } else if !hasMoreData, !historyGroups.isEmpty {
                        let totalCount = historyGroups.flatMap(\.items).count
                        Text("没有更多历史记录了 (共\(totalCount)条)")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .padding(.vertical, DesignSystem.Spacing.lg)
                    }
                }
                .padding(.vertical, DesignSystem.Spacing.sm)
            }
        }
    }

    // MARK: - 加载内容视图（骨架屏）

    @ViewBuilder
    private var loadingContentView: some View {
        ScrollView {
            LazyVStack(spacing: DesignSystem.Spacing.md) {
                ForEach(0..<3, id: \.self) { _ in
                    HistorySkeletonGroupView()
                }
            }
            .padding(.vertical, DesignSystem.Spacing.sm)
        }
        .redacted(reason: .placeholder)
    }
}

// MARK: - 历史记录骨架屏组件

private struct HistorySkeletonGroupView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 日期标题骨架屏
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(width: 80, height: 16)

                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.sm)

            // 历史记录项骨架屏
            ForEach(0..<2, id: \.self) { _ in
                HistorySkeletonItemView()
            }
        }
    }
}

private struct HistorySkeletonItemView: View {
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            // 模型图标
            RoundedRectangle(cornerRadius: DesignSystem.Rounded.sm)
                .fill(DesignSystem.Colors.backgroundCard)
                .frame(width: 32, height: 32)

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                // 标题
                RoundedRectangle(cornerRadius: 4)
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(height: 18)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .scaleEffect(x: 0.8, anchor: .leading)

                // 时间和模型信息
                HStack(spacing: DesignSystem.Spacing.sm) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 60, height: 14)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.Colors.border.opacity(0.3))
                        .frame(width: 40, height: 14)
                }
            }

            Spacer()
        }
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
    }
}

// MARK: - 历史记录空状态视图

private struct HistoryEmptyView: View {
    var body: some View {
        EmptyStateView(
            iconName: "list.bullet.clipboard",
            title: "暂无历史记录",
            description: "您的对话历史记录会显示在这里",
            style: .fullScreen
        )
    }
}

// MARK: - 历史记录分组视图

private struct HistoryGroupView: View {
    let date: String
    let items: [HistoryItem]
    let isSelectionMode: Bool
    let selectedItems: Set<Int>
    let availableModels: [LLMRes]
    let onItemTap: (HistoryItem) -> Void
    let onItemDelete: (HistoryItem) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text(formatDate(date))
                    .font(DesignSystem.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(DesignSystem.Colors.textTertiary)
                    .textCase(.uppercase)

                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.lg)
            .padding(.bottom, DesignSystem.Spacing.xs)

            ForEach(items) { item in
                HistoryItemView(
                    item: item,
                    isSelectionMode: isSelectionMode,
                    isSelected: selectedItems.contains(item.id),
                    availableModels: availableModels,
                    onTap: { onItemTap(item) },
                    onDelete: { onItemDelete(item) }
                )
            }
        }
    }

    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        if let date = formatter.date(from: dateString) {
            if Calendar.current.isDateInToday(date) {
                return "今天"
            } else if Calendar.current.isDateInYesterday(date) {
                return "昨天"
            } else {
                let displayFormatter = DateFormatter()
                displayFormatter.dateFormat = "MM月dd日"
                return displayFormatter.string(from: date)
            }
        }

        return dateString
    }
}

// MARK: - 历史记录项视图

private struct HistoryItemView: View {
    let item: HistoryItem
    let isSelectionMode: Bool
    let isSelected: Bool
    let availableModels: [LLMRes]
    let onTap: () -> Void
    let onDelete: () -> Void

    private var llmModel: LLMRes? {
        availableModels.first { $0.id == item.modelId }
    }

    var body: some View {
        HStack(spacing: DesignSystem.Spacing.md) {
            ModelIconView(
                iconURL: llmModel?.icon,
                provider: llmModel?.safeProvider ?? item.safeModelName,
                size: 28,
                cornerRadius: 6
            )

            VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                Text(item.safeTitle)
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                HStack(spacing: DesignSystem.Spacing.sm) {
                    Text(item.safeModelName)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("•")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textTertiary)

                    Text(item.formattedCreateTime)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }

            Spacer()

            if isSelectionMode {
                selectionIndicator
            }
        }
        .contentShape(Rectangle())
        .padding(.horizontal, DesignSystem.Spacing.lg)
        .padding(.vertical, DesignSystem.Spacing.md)
        .background(
            Group {
                if isSelected {
                    DesignSystem.Colors.primary.opacity(0.1)
                } else {
                    Color.clear
                }
            }
        )
        .overlay(
            Rectangle()
                .fill(DesignSystem.Colors.separator.opacity(0.3))
                .frame(height: 0.5)
                .padding(.leading, DesignSystem.Spacing.lg + 32 + DesignSystem.Spacing.md),
            alignment: .bottom
        )
        .onTapGesture(perform: onTap)
        .accessibilityAddTraits(.isButton)
    }

    // MARK: - 选择指示器

    @ViewBuilder
    var selectionIndicator: some View {
        ZStack {
            if isSelected {
                Circle()
                    .fill(DesignSystem.Colors.primary)
                    .frame(width: 24, height: 24)

                Image(systemName: "checkmark")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(.white)
            } else {
                Circle()
                    .fill(DesignSystem.Colors.border.opacity(0.3))
                    .frame(width: 24, height: 24)
            }
        }
    }
}
