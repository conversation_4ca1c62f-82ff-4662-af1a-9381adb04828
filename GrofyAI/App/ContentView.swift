import FlowStacks
import SwiftUI

// 主应用入口
struct ContentView: View {
    @State private var selectedTab: TabItem = .chat
    @State private var flowPath = FlowPath()

    @StateObject private var sharedKnowledgeController = KnowledgeTabController()
    @StateObject private var dropDownCoordinator = DropDownCoordinator()

    @EnvironmentObject private var globalAuthManager: GlobalAuthManager

    @State private var chatSettingsManager: ChatSettingsManager?

    var body: some View {
        FlowStack($flowPath, withNavigation: true) {
            TabView(selection: $selectedTab) {
                // 聊天标签
                ChatTabView()
                    .tabItem {
                        AdaptiveTabItemView(
                            tabItem: TabItem.chat,
                            isSelected: selectedTab == TabItem.chat
                        )
                    }
                    .tag(TabItem.chat)
                // 图像生成
                ImageAiView()
                    .tabItem {
                        AdaptiveTabItemView(
                            tabItem: TabItem.image,
                            isSelected: selectedTab == TabItem.image
                        )
                    }
                    .tag(TabItem.image)
                // 知识库
                KnowledgeTabView()
                    .environmentObject(sharedKnowledgeController)
                    .tabItem {
                        AdaptiveTabItemView(
                            tabItem: TabItem.knowledge,
                            isSelected: selectedTab == TabItem.knowledge
                        )
                    }
                    .tag(TabItem.knowledge)
                // 视频生成
                VideoAiView()
                    .tabItem {
                        AdaptiveTabItemView(
                            tabItem: TabItem.video,
                            isSelected: selectedTab == TabItem.video
                        )
                    }
                    .tag(TabItem.video)
                // 我的
                ProfileView()
                    .tabItem {
                        AdaptiveTabItemView(
                            tabItem: TabItem.profile,
                            isSelected: selectedTab == TabItem.profile
                        )
                    }
                    .tag(TabItem.profile)
            }
            .flowDestination(for: Route.self) { route in
                switch route {
                // 支付
                case .payment:
                    PaymentView()
                // 设置
                case .settings:
                    SettingsView()
                // 鉴权
                case .authentication:
                    AuthenticationView()
                // 对话
                case .textChat(let initialMessage, let threadId):
                    TextChatView(initialMessage: initialMessage, threadId: threadId)
                // 文件对话
                case .fileChat(let knowledgeId, let fileName, let initialMessage, let threadId):
                    FileChatView(
                        knowledgeId: knowledgeId,
                        fileName: fileName,
                        initialMessage: initialMessage,
                        threadId: threadId
                    )
                // 对话式图片生成
                case .imageGeneratorChat(let initialMessage, let threadId):
                    ImageChatView(initialMessage: initialMessage, threadId: threadId)
                // 对话历史记录
                case .chatHistory(let chatMode):
                    HistoryView(chatMode: chatMode)
                // 图片识别
                case .imageRecognitionChat(let threadId):
                    ImageRecognitionChat(threadId: threadId)
                // 图片识别历史记录
                case .imageRecognitionHistoryList:
                    ImageRecognitionHistoryList()
                // 图片识别历史记录（设置页面入口）
                case .imageRecognitionHistory:
                    ImageRecognitionHistoryList()
                // 积分使用记录
                case .creditsHistory:
                    CreditsHistoryView()
                // 图片创建
                case .createImage(let workDetail):
                    ImageCreationView(detail: workDetail)
                        .environment(\.dropDownCoordinator, dropDownCoordinator)
                case .imageArtWorkCreationResult(let transactionID, let detail):
                    ImageArtworkCreationResult(transactionID: transactionID, detail: detail)
                // 视频创建
                case .createVideo(let workDetail, let createType):
                    VideoCreationView(detail: workDetail, createType: createType)
                        .environment(\.dropDownCoordinator, dropDownCoordinator)
                // 特效视频
                case .effectVideo:
                    EffectVideoView()
                // 创建特效视频
                case .createEffectVideo(let type, let workDetail):
                    CreateEffectView(type: type, detail: workDetail)
                // 视频推荐
                case .videoRecommendedArtwork(let workDetail):
                    VideoRecommendedArtworkView(artwork: workDetail)
                // 视频作品创建结果
                case .videoArtWorkCreationResult(let transactionID, let detail):
                    VideoArtworkCreationResult(transactionID: transactionID, detail: detail)
                // 历史记录
                case .artWorkHistory(let defaultModel):
                    CreationHistoryView(defaultModel: defaultModel)
                // 知识库
                case .personalKnowledgeBase:
                    PersonalKnowledgeBaseView()
                // 知识库文件
                case .knowledgeBaseFiles(let categoryId, let categoryName):
                    KnowledgeBaseFilesView(
                        categoryId: categoryId,
                        navigationTitle: categoryName.map { "\($0) 知识库" } ?? "知识库文件"
                    )
                // 编辑知识库分类
                case .editKnowledgeCategory(let categoryId):
                    EditKnowledgeCategoryScreen(categoryId: categoryId)
                        .environmentObject(sharedKnowledgeController)
                // 创建知识库分类
                case .createKnowledgeCategory:
                    CreateKnowledgeCategoryScreen()
                        .environmentObject(sharedKnowledgeController)
                // 设置详情页面
                case .themeSetting:
                    ThemeSettingView()
                case .aboutUs:
                    AboutUsView()
                // 快捷跳转
                case .imageGeneration:
                    PlaceholderChatView(
                        title: "图片生成",
                        description: "AI智能生成高质量图片，支持多种风格和尺寸",
                        iconName: "photo.on.rectangle.angled",
                        iconColor: .purple
                    )
                case .videoGeneration:
                    PlaceholderChatView(
                        title: "视频生成",
                        description: "AI智能生成视频内容，支持多种场景和风格",
                        iconName: "video",
                        iconColor: .red
                    )
                case .audioGeneration:
                    PlaceholderChatView(
                        title: "音频生成",
                        description: "AI智能生成音频内容，支持多种音效和音乐",
                        iconName: "music.note",
                        iconColor: .orange
                    )
                case .imageBook:
                    PlaceholderChatView(
                        title: "图书",
                        description: "智能图书阅读和管理功能",
                        iconName: "book",
                        iconColor: .indigo
                    )
                case .audioMusic:
                    PlaceholderChatView(
                        title: "音频",
                        description: "音频播放和管理功能",
                        iconName: "music.note",
                        iconColor: .pink
                    )
                case .problemFeedback:
                    ProblemFeedbackView()
                default:
                    EmptyView()
                }
            }
        }
        .withToast()
        .fullScreenCover(isPresented: $globalAuthManager.shouldShowAuthentication) {
            AuthenticationView()
                .onDisappear {
                    globalAuthManager.dismissAuthentication()
                }
        }
        .task {
            // 延迟初始化非关键组件
            await initializeDelayedComponents()
        }
    }

    // MARK: - 延迟初始化

    private func initializeDelayedComponents() async {
        // 延迟100ms初始化ChatSettingsManager
        try? await Task.sleep(nanoseconds: 100_000_000)

        await MainActor.run {
            if chatSettingsManager == nil {
                chatSettingsManager = ChatSettingsManager()
            }
        }
    }
}

struct AdaptiveTabItemView: View {
    let tabItem: TabItem
    let isSelected: Bool

    var body: some View {
        VStack(spacing: 4) {
            Image(isSelected ? tabItem.selectedIcon : tabItem.icon)
                .renderingMode(.template)
                .font(.system(size: 24))

            Text(tabItem.title)
                .font(.system(size: 10, weight: .medium))
        }
    }
}
