import Foundation
import SwiftUI

// MARK: - 全局模型管理器

@MainActor
class ModelManager: ObservableObject {
    static let shared = ModelManager()

    // MARK: - Published Properties

    @Published var availableModels: [LLMRes] = []
    @Published var selectedModel: LLMRes?
    @Published var isLoading = false
    @Published var errorMessage: String?

    // MARK: - Private Properties

    private let llmService = LLMService()
    private let iconPreloader = ModelIconPreloader.shared
    private var lastRequestTime: Date?
    private let requestCooldown: TimeInterval = 5.0 // 5秒内不重复请求
    private var loadingTask: Task<Void, Never>?

    private init() {
        setupNotificationObservers()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
        loadingTask?.cancel()
    }

    // MARK: - 后台加载模型

    func loadModelsInBackground() {
        guard loadingTask == nil else { return }

        loadingTask = Task {
            await Task.detached(priority: .background) {
                await self.loadModels(showLoading: false)
            }.value
        }
    }

    // MARK: - Public Methods

    /// 加载可用模型列表（优化版）
    func loadModels(force: Bool = false, showLoading: Bool = true) async {
        if !force, let lastTime = lastRequestTime, Date().timeIntervalSince(lastTime) < requestCooldown {
            print("ModelManager: 请求过于频繁，跳过重复请求（距离上次请求 \(Date().timeIntervalSince(lastTime))秒）")
            return
        }

        if isLoading, !force {
            print("ModelManager: 正在加载中，跳过重复请求")
            return
        }

        lastRequestTime = Date()

        if showLoading {
            isLoading = true
        }
        errorMessage = nil

        do {
            let models = try await llmService.getModelList()

            await MainActor.run {
                self.availableModels = models
                if showLoading {
                    self.isLoading = false
                }

                // 如果当前没有选择模型或选择的模型不在列表中，选择第一个可用模型
                if self.selectedModel == nil || !models.contains(where: { $0.id == self.selectedModel?.id }) {
                    self.selectedModel = models.first
                }

                // 后台预加载图标
                Task.detached(priority: .background) {
                    await self.iconPreloader.preloadModelIcons(for: models)
                }

                // 发送模型列表更新通知
                NotificationCenter.default.post(name: .modelListUpdated, object: nil)
            }

        } catch {
            await MainActor.run {
                if showLoading {
                    self.isLoading = false
                }
                self.errorMessage = error.localizedDescription
                print("❌ ModelManager: 加载模型列表失败: \(error.localizedDescription)")

                // 加载失败时不清空列表，保持之前的数据
                if self.availableModels.isEmpty {
                    self.availableModels = []
                    self.selectedModel = nil
                }
            }
        }
    }

    /// 选择模型
    func selectModel(_ model: LLMRes) {
        selectedModel = model
    }

    /// 获取当前选择的模型显示信息
    func getSelectedModelDisplayInfo() -> (name: String, icon: String, color: Color) {
        guard let model = selectedModel else {
            return ("暂无可用模型", "brain.head.profile", .gray)
        }

        let icon = getModelIcon(for: model)
        let color: Color = {
            switch model.safeProvider.lowercased() {
            case "openai":
                return .blue
            case "anthropic":
                return .orange
            case "google":
                return .green
            default:
                return .gray
            }
        }()

        return (model.safeName, icon, color)
    }

    /// 根据ID获取模型
    func getModelById(_ id: Int) -> LLMRes? {
        return availableModels.first { $0.id == id }
    }

    /// 获取当前选择的模型ID
    func getSelectedModelId() -> Int? {
        return selectedModel?.id
    }

    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            forName: .userAuthenticationReady,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                // 用户认证后，在后台加载模型
                self?.loadModelsInBackground()
            }
        }

        NotificationCenter.default.addObserver(
            forName: .authenticationRequired,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                // 保持现有数据，不清空
                self?.selectedModel = nil
            }
        }

        NotificationCenter.default.addObserver(
            forName: .requestModelListLoad,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                guard let self else { return }
                if self.availableModels.isEmpty, !self.isLoading {
                    await self.loadModels()
                }
            }
        }
    }

    /// 获取模型图标
    private func getModelIcon(for model: LLMRes) -> String {
        if let icon = model.icon, !icon.isEmpty, icon.hasPrefix("http") {
            return icon
        }

        switch model.safeProvider.lowercased() {
        case "openai":
            return "brain.head.profile"
        case "anthropic":
            return "lightbulb.fill"
        case "google":
            return "star.fill"
        default:
            return "brain.head.profile"
        }
    }
}
