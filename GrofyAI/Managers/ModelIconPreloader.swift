import Foundation
import Kingfisher
import SwiftUI

// MARK: - 模型图标预加载管理器

@MainActor
class ModelIconPreloader: ObservableObject {
    static let shared = ModelIconPreloader()

    @Published var isPreloading = false
    @Published var preloadProgress = 0.0
    @Published var preloadedCount = 0

    private var preloadedURLs: Set<String> = []
    private var memoryWarningObserver: NSObjectProtocol?

    private init() {
        setupMemoryWarningObserver()
    }

    deinit {
        if let observer = memoryWarningObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - Public Methods

    /// 预加载模型图标
    /// - Parameter models: 模型列表
    func preloadModelIcons(for models: [LLMRes]) {
        let validIconURLs = models.compactMap { model -> URL? in
            guard let iconString = model.icon,
                  !iconString.isEmpty,
                  iconString.hasPrefix("http"),
                  !preloadedURLs.contains(iconString),
                  let url = URL(string: iconString)
            else {
                return nil
            }
            return url
        }

        guard !validIconURLs.isEmpty else {
            return
        }

        isPreloading = true
        preloadProgress = 0.0
        preloadedCount = 0

        preloadIcons(urls: validIconURLs)
    }

    /// 逐个预加载图标
    /// - Parameter urls: 图标URL列表
    private func preloadIcons(urls: [URL]) {
        let totalCount = urls.count

        // 使用actor-isolated属性来跟踪进度
        Task { @MainActor in
            var completedCount = 0
            var succeededCount = 0
            var failedCount = 0

            for url in urls {
                KingfisherManager.shared.retrieveImage(
                    with: url,
                    options: [
                        .cacheMemoryOnly,
                        .backgroundDecode,
                        .scaleFactor(UIScreen.main.scale),
                    ]
                ) { result in
                    Task { @MainActor in
                        completedCount += 1

                        switch result {
                        case .success:
                            self.preloadedURLs.insert(url.absoluteString)
                            succeededCount += 1
                        case .failure(let error):
                            failedCount += 1
                            print("⚠️ ModelIconPreloader: 预加载失败 - \(url.absoluteString): \(error.localizedDescription)")
                        }

                        // 更新进度
                        self.preloadedCount = succeededCount
                        self.preloadProgress = Double(completedCount) / Double(totalCount)

                        // 检查是否全部完成
                        if completedCount == totalCount {
                            self.handlePreloadCompletion(
                                total: totalCount,
                                succeeded: succeededCount,
                                failed: failedCount
                            )
                        }
                    }
                }
            }
        }
    }

    /// 清除预加载缓存
    func clearPreloadCache() {
        preloadedURLs.removeAll()

        // 清除Kingfisher内存缓存中的模型图标
        ImageCache.default.clearMemoryCache()
    }

    /// 检查图标是否已预加载
    /// - Parameter iconURL: 图标URL
    /// - Returns: 是否已预加载
    func isIconPreloaded(_ iconURL: String?) -> Bool {
        guard let iconURL else { return false }
        return preloadedURLs.contains(iconURL)
    }

    // MARK: - Private Methods

    /// 处理预加载完成
    private func handlePreloadCompletion(
        total: Int,
        succeeded: Int,
        failed: Int
    ) {
        isPreloading = false
        preloadProgress = 1.0
    }

    /// 设置内存警告观察者
    private func setupMemoryWarningObserver() {
        memoryWarningObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            print("⚠️ ModelIconPreloader: 收到内存警告，清除预加载缓存")
            Task { @MainActor in
                self?.clearPreloadCache()
            }
        }
    }
}
