import Foundation

// MARK: - 格式支持类型

struct FormatSupport: Codable {
    let text: Bool
    let image: Bool
    let audio: Bool
    let function: Bool
}

// MARK: - 模型级别枚举

enum ModelLevel: String, Codable {
    case basic = "BASIC"
    case advanced = "ADVANCED"
    case expert = "EXPERT"

    var displayName: String {
        switch self {
        case .basic: return "基础"
        case .advanced: return "高级"
        case .expert: return "专家"
        }
    }
}

// MARK: - LLM响应类型

struct LLMRes: Codable {
    let id: Int
    let name: String?
    let code: String?
    let provider: String?
    let releaseDate: String?
    let icon: String?
    let summary: String?
    let level: ModelLevel?
    let inputFormat: FormatSupport?
    let outputFormat: FormatSupport?

    private enum CodingKeys: String, CodingKey {
        case id
        case name
        case code
        case provider
        case icon
        case summary
        case level
        case releaseDate
        case inputFormat
        case outputFormat
    }

    // MARK: - 便利属性，提供默认值

    /// 安全的模型名称，提供默认值
    var safeName: String {
        return name ?? "未知模型"
    }

    /// 安全的模型代码，提供默认值
    var safeCode: String {
        return code ?? "unknown"
    }

    /// 安全的提供商名称，提供默认值
    var safeProvider: String {
        return provider ?? "未知提供商"
    }

    /// 安全的摘要，提供默认值
    var safeSummary: String {
        return summary ?? "暂无描述"
    }

    /// 安全的级别，提供默认值
    var safeLevel: ModelLevel {
        return level ?? .basic
    }
}
