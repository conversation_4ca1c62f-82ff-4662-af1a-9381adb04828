import SwiftUI

// MARK: - 语音服务环境配置

struct SpeechServicesEnvironment: ViewModifier {
    @StateObject private var ttsService = TextToSpeechService()
    @StateObject private var sttService = SpeechRecognitionService()

    func body(content: Content) -> some View {
        content
            .environmentObject(ttsService)
            .environmentObject(sttService)
            .task{
                await ttsService.warmup()
            }
            .onReceive(NotificationCenter.default
                .publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            ) { _ in
                // 内存警告时清理资源
                TTSCache().clearCache()
                sttService.cleanup()
            }
    }
}

extension View {
    func withSpeechServices() -> some View {
        modifier(SpeechServicesEnvironment())
    }
}
