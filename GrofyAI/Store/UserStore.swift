import Alamofire
import Combine
import Foundation
import KeychainAccess
import SwiftUI

class AuthStore: ObservableObject {
    static let shared = AuthStore()

    @Published var user: LoginRes

    private let keychain = Keychain(service: AppConfig.Auth.keychainService)
    private var refreshTask: Task<String, Error>?
    private let refreshTokenService = RefreshTokenService()

    private enum Keys {
        static let accessToken = "accessToken"
        static let refreshToken = "refreshToken"
        static let memberLevel = "memberLevel"
        static let unionId = "unionId"
        static let expireDate = "expireDate"
        static let credits = "credits"
        static let premiumCredits = "premiumCredits"
        static let giftedCredits = "giftedCredits"
        static let giftedPremiumCredits = "giftedPremiumCredits"
    }

    private init() {
        let expireDateString = (try? keychain.get(Keys.expireDate)) ?? "0"

        let credits = (try? keychain.get(Keys.credits)).flatMap { Int($0) } ?? 0
        let premiumCredits = (try? keychain.get(Keys.premiumCredits)).flatMap { Int($0) } ?? 0
        let giftedCredits = (try? keychain.get(Keys.giftedCredits)).flatMap { Int($0) } ?? 0
        let giftedPremiumCredits = (try? keychain.get(Keys.giftedPremiumCredits)).flatMap { Int($0) } ?? 0

        user = LoginRes(
            accessToken: try? keychain.get(Keys.accessToken),
            refreshToken: try? keychain.get(Keys.refreshToken),
            memberLevel: try? keychain.get(Keys.memberLevel),
            unionId: try? keychain.get(Keys.unionId),
            expireDate: Int(expireDateString),
            credits: credits,
            premiumCredits: premiumCredits,
            giftedCredits: giftedCredits,
            giftedPremiumCredits: giftedPremiumCredits
        )
    }

    // MARK: - 用户数据管理

    func updateUser(user: LoginRes) {
        self.user = user
        saveToKeychain()
    }

    private func saveToKeychain() {
        do {
            if let token = user.accessToken {
                try keychain
                    .label("accessToken")
                    .accessibility(.afterFirstUnlock)
                    .set(token, key: Keys.accessToken)
            } else {
                try keychain.remove(Keys.accessToken)
            }

            if let refreshToken = user.refreshToken {
                try keychain
                    .label("refreshToken")
                    .accessibility(.afterFirstUnlock)
                    .set(refreshToken, key: Keys.refreshToken)
            } else {
                try keychain.remove(Keys.refreshToken)
            }

            if let level = user.memberLevel {
                try keychain
                    .label("memberLevel")
                    .accessibility(.afterFirstUnlock)
                    .set(level, key: Keys.memberLevel)
            } else {
                try keychain.remove(Keys.memberLevel)
            }

            if let id = user.unionId {
                try keychain
                    .label("unionId")
                    .accessibility(.afterFirstUnlock)
                    .set(id, key: Keys.unionId)
            } else {
                try keychain.remove(Keys.unionId)
            }

            if let date = user.expireDate {
                try keychain
                    .label("expireDate")
                    .accessibility(.afterFirstUnlock)
                    .set(String(date), key: Keys.expireDate)
            } else {
                try keychain.remove(Keys.expireDate)
            }

            let creditsValue = user.credits ?? 0
            try keychain
                .label("credits")
                .accessibility(.afterFirstUnlock)
                .set(String(creditsValue), key: Keys.credits)

            let premiumCreditsValue = user.premiumCredits ?? 0
            try keychain
                .label("premiumCredits")
                .accessibility(.afterFirstUnlock)
                .set(String(premiumCreditsValue), key: Keys.premiumCredits)

            let giftedCreditsValue = user.giftedCredits ?? 0
            try keychain
                .label("giftedCredits")
                .accessibility(.afterFirstUnlock)
                .set(String(giftedCreditsValue), key: Keys.giftedCredits)

            let giftedPremiumCreditsValue = user.giftedPremiumCredits ?? 0
            try keychain
                .label("giftedPremiumCredits")
                .accessibility(.afterFirstUnlock)
                .set(String(giftedPremiumCreditsValue), key: Keys.giftedPremiumCredits)
        } catch {
            print("保存用户数据到Keychain失败: \(error)")
        }
    }

    // MARK: - 认证管理功能

    func validToken() async throws -> String {
        if let token = getAccessToken() { return token }

        // 防止并发重复刷新
        if let existingTask = refreshTask {
            return try await existingTask.value
        }

        refreshTask = Task {
            defer { refreshTask = nil }
            return try await refresh()
        }

        return try await refreshTask!.value
    }

    private func refresh() async throws -> String {
        guard let refreshToken = user.refreshToken else {
            throw NetworkError.authorizationError
        }

        let res = try await refreshTokenService.refreshToken(refreshToken: refreshToken)

        guard let newAccessToken = res.accessToken
        else {
            throw NetworkError.authorizationError
        }

        let updatedUser = RefreshTokenRes(
            accessToken: res.accessToken,
            refreshToken: res.refreshToken,
            memberLevel: res.memberLevel,
            unionId: res.unionId,
            expireDate: res.expireDate,
            credits: res.credits,
            premiumCredits: res.premiumCredits,
            giftedCredits: res.giftedCredits,
            giftedPremiumCredits: res.giftedPremiumCredits
        )

        DispatchQueue.main.async {
            self.updateUser(user: updatedUser)
        }

        return newAccessToken
    }

    func refreshTokenOnAppLaunch() async {
        guard user.accessToken != nil, user.refreshToken != nil else {
            print("没有找到登录信息，跳过 token 刷新")
            return
        }

        do {
            _ = try await validToken()
            print("accessToken 刷新成功")
            // 发送认证就绪通知
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .userAuthenticationReady, object: nil)
            }
        } catch {
            print("accessToken 刷新失败: \(error)")
            DispatchQueue.main.async {
                self.clearUser()
                NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
            }
        }
    }

    func refreshFromKeychain() {
        let expireDateString = (try? keychain.get(Keys.expireDate)) ?? "0"

        let credits = (try? keychain.get(Keys.credits)).flatMap { Int($0) } ?? 0
        let premiumCredits = (try? keychain.get(Keys.premiumCredits)).flatMap { Int($0) } ?? 0
        let giftedCredits = (try? keychain.get(Keys.giftedCredits)).flatMap { Int($0) } ?? 0
        let giftedPremiumCredits = (try? keychain.get(Keys.giftedPremiumCredits)).flatMap { Int($0) } ?? 0

        user = LoginRes(
            accessToken: try? keychain.get(Keys.accessToken),
            refreshToken: try? keychain.get(Keys.refreshToken),
            memberLevel: try? keychain.get(Keys.memberLevel),
            unionId: try? keychain.get(Keys.unionId),
            expireDate: Int(expireDateString),
            credits: credits,
            premiumCredits: premiumCredits,
            giftedCredits: giftedCredits,
            giftedPremiumCredits: giftedPremiumCredits
        )
    }

    func clearUser() {
        DispatchQueue.main.async { [weak self] in
            self?.user = LoginRes()
        }

        do {
            try keychain.remove(Keys.accessToken)
            try keychain.remove(Keys.refreshToken)
            try keychain.remove(Keys.memberLevel)
            try keychain.remove(Keys.unionId)
            try keychain.remove(Keys.expireDate)
            try keychain.remove(Keys.credits)
            try keychain.remove(Keys.premiumCredits)
            try keychain.remove(Keys.giftedCredits)
            try keychain.remove(Keys.giftedPremiumCredits)
        } catch {
            print("清除Keychain用户数据失败: \(error)")
        }
    }

    func getAccessToken() -> String? {
        return user.accessToken
    }

    func getRefreshToken() -> String? {
        return user.refreshToken
    }

    func getMemberLevel() -> String? {
        return user.memberLevel
    }

    func getUnionId() -> String? {
        return user.unionId
    }

    func getCredits() -> Int {
        return user.credits ?? 0
    }

    func getPremiumCredits() -> Int {
        return user.premiumCredits ?? 0
    }

    func getGiftedCredits() -> Int {
        return user.giftedCredits ?? 0
    }

    func getGiftedPremiumCredits() -> Int {
        return user.giftedPremiumCredits ?? 0
    }

    /// @description: 获取过期时间
    func getExpireDate() -> Int? {
        return user.expireDate
    }

    /// @description: 是否是VIP或更高级别会员（兼容旧代码）
    func isVip() -> Bool {
        return MembershipLevel.isVIP(user.memberLevel)
    }

    /// 获取会员等级枚举
    func getMembershipLevel() -> MembershipLevel {
        return MembershipLevel.from(user.memberLevel)
    }

    /// 是否为付费会员（任何付费等级）
    func isPaidMember() -> Bool {
        return MembershipLevel.isPaidMember(user.memberLevel)
    }

    /// 是否为免费用户
    func isFreeMember() -> Bool {
        return MembershipLevel.isFree(user.memberLevel)
    }

    /// 是否为PRO或更高级别会员
    func isProOrHigher() -> Bool {
        return getMembershipLevel().isEqualOrHigherThan(.pro)
    }

    /// 是否为PLUS或更高级别会员
    func isPlusOrHigher() -> Bool {
        return getMembershipLevel().isEqualOrHigherThan(.plus)
    }

    /// 是否为ULTRA会员
    func isUltra() -> Bool {
        return getMembershipLevel() == .ultra
    }

    /// 获取会员等级显示名称
    func getMembershipDisplayName() -> String {
        return MembershipLevel.displayName(for: user.memberLevel)
    }

    // MARK: - 更新会员状态（内购后调用）

    func updateMemberStatus(memberLevel: String?, credits: Int?, premiumCredits: Int?, expireDate: Int?) {
        // 更新内存中的用户信息
        if let memberLevel {
            user.memberLevel = memberLevel
            try? keychain.set(memberLevel, key: Keys.memberLevel)
        }

        if let credits {
            user.credits = credits
            try? keychain.set(String(credits), key: Keys.credits)
        }

        if let premiumCredits {
            user.premiumCredits = premiumCredits
            try? keychain.set(String(premiumCredits), key: Keys.premiumCredits)
        }

        if let expireDate {
            user.expireDate = expireDate
            try? keychain.set(String(expireDate), key: Keys.expireDate)
        }

        // 触发UI更新
        objectWillChange.send()
    }
}
