import Foundation
import SwiftUI
// MARK: - 消息图片附件模型

/// 消息中的图片附件（用于images字段）
struct MessageImage: Codable, Identifiable, Hashable {
    let fileId: String
    let fileName: String
    let fileUrl: String
    let localImage: UIImage?

    // 计算属性提供与MessageFile兼容的接口
    var id: String { fileId }
    var name: String { fileName }
    var url: String { fileUrl }
    var type: String { "image" }

    enum CodingKeys: String, CodingKey {
        case fileId = "file_id"
        case fileName = "file_name"
        case fileUrl = "file_url"
    }

    init(fileId: String, fileName: String, fileUrl: String, localImage: UIImage? = nil) {
        self.fileId = fileId
        self.fileName = fileName
        self.fileUrl = fileUrl
        self.localImage = localImage
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        fileId = try container.decode(String.self, forKey: .fileId)
        fileName = try container.decode(String.self, forKey: .fileName)
        fileUrl = try container.decode(String.self, forKey: .fileUrl)
        
        self.localImage = nil
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(fileId, forKey: .fileId)
        try container.encode(fileName, forKey: .fileName)
        try container.encode(fileUrl, forKey: .fileUrl)
    }
}

// MARK: - 文件类型扩展

extension MessageImage {
    /// 获取文件图标名称
    var iconName: String {
        return "photo"
    }

    /// 获取文件类型的中文显示名称
    var displayName: String {
        return "图片"
    }

    /// 转换为MessageFile格式（用于UI兼容）
    func toMessageFile() -> MessageFile {
        return MessageFile(id: fileId, name: fileName, type: "image", url: fileUrl, localImage: localImage)
    }
}
