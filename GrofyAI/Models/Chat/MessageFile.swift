import Foundation
import SwiftUI


// MARK: - 消息文件附件模型

/// 消息中的文件附件
struct MessageFile: Codable, Identifiable, Hashable {
    let id: String
    let name: String
    let type: String
    let url: String
    
    // --- 仅在本地使用的属性 ---
    // 这个属性不包含在下面的 CodingKeys 中，因此 Codable 会自动忽略它
    let localImage: UIImage?
    
    // 定义 CodingKeys，只列出需要参与编解码的属性
    private enum CodingKeys: String, CodingKey {
        case id
        case name
        case type
        case url
    }
    
    init(id: String, name: String, type: String, url: String, localImage: UIImage? = nil) {
        self.id = id
        self.name = name
        self.type = type
        self.url = url
        self.localImage = localImage
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(String.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.type = try container.decode(String.self, forKey: .type)
        self.url = try container.decode(String.self, forKey: .url)
        
        // localImage 不是来自 JSON，所以解码时将其设为 nil
        self.localImage = nil
    }
    
    // 为了让 MessageFile 符合 Hashable，我们需要告诉它如何哈希
    func hash(into hasher: inout Hasher) {
        hasher.combine(id) // 通常只哈希唯一的 id 就足够了
    }
    
    // 为了让 MessageFile 符合 Equatable (Hashable 的一部分)，我们需要告诉它如何比较
    static func == (lhs: MessageFile, rhs: MessageFile) -> Bool {
        return lhs.id == rhs.id // 同样，只比较 id
    }
}

// MARK: - 文件类型扩展

extension MessageFile {
    /// 文件类型枚举 - 对应前端的FileCategory
    enum FileCategory: String, CaseIterable {
        case csv = "CSV"
        case epub = "EPUB"
        case html = "HTML"
        case image = "IMAGE"
        case markdown = "MARKDOWN"
        case excel = "EXCEL"
        case pdf = "PDF"
        case word = "WORD"
        case ppt = "PPT"
        case audio = "AUDIO"
        case video = "VIDEO"
        case zip = "ZIP"
        case url = "URL"
        case youtube = "YOUTUBE"
        case unknown = "UNKNOWN"

        init(from string: String) {
            self = FileCategory(rawValue: string.uppercased()) ?? .unknown
        }
    }

    /// 获取文件类别
    var fileCategory: FileCategory {
        return FileCategory(from: type)
    }

    /// 是否为图片文件
    var isImage: Bool {
        return fileCategory == .image
    }

    /// 是否为文档文件
    var isDocument: Bool {
        switch fileCategory {
        case .csv, .epub, .excel, .html, .markdown, .pdf, .ppt, .word:
            return true
        default:
            return false
        }
    }

    /// 是否为媒体文件
    var isMedia: Bool {
        switch fileCategory {
        case .audio, .image, .video:
            return true
        default:
            return false
        }
    }

    /// 获取文件图标名称
    var iconName: String {
        switch fileCategory {
        case .image:
            return "photo"
        case .pdf:
            return "doc.richtext"
        case .word:
            return "doc.text"
        case .csv, .excel:
            return "tablecells"
        case .ppt:
            return "rectangle.on.rectangle"
        case .video:
            return "video"
        case .audio:
            return "music.note"
        case .zip:
            return "archivebox"
        case .html:
            return "globe"
        case .markdown:
            return "text.alignleft"
        case .epub:
            return "book"
        case .url:
            return "link"
        case .youtube:
            return "play.rectangle"
        case .unknown:
            return "doc"
        }
    }

    /// 获取文件类型的中文显示名称
    var displayName: String {
        return fileCategory.displayName
    }

    /// 获取文件类型颜色
    var typeColor: String {
        switch fileCategory {
        case .image:
            return "blue"
        case .pdf:
            return "red"
        case .word:
            return "blue"
        case .csv, .excel:
            return "green"
        case .ppt:
            return "orange"
        case .video:
            return "purple"
        case .audio:
            return "pink"
        case .zip:
            return "gray"
        case .html, .url:
            return "cyan"
        case .markdown:
            return "indigo"
        case .epub:
            return "brown"
        case .youtube:
            return "red"
        case .unknown:
            return "gray"
        }
    }
}

// MARK: - MessageFile扩展 - 文件上传支持

extension MessageFile.FileCategory {
    /// 根据文件扩展名推断文件类别
    /// - Parameter fileExtension: 文件扩展名（不含点号）
    /// - Returns: 对应的文件类别
    static func from(fileExtension: String) -> MessageFile.FileCategory {
        let ext = fileExtension.lowercased()

        switch ext {
        // PDF文档
        case "pdf":
            return .pdf

        // Word文档
        case "doc", "docx":
            return .word

        // Excel表格
        case "xls", "xlsx":
            return .excel

        // PowerPoint演示文稿
        case "ppt", "pptx":
            return .ppt

        // CSV表格
        case "csv":
            return .csv

        // 图片文件
        case "bmp", "gif", "ico", "jpeg", "jpg", "png", "svg", "tif", "tiff", "webp":
            return .image

        // 音频文件
        case "aac", "flac", "m4a", "mp3", "ogg", "wav", "wma":
            return .audio

        // 视频文件
        case "avi", "flv", "m4v", "mkv", "mov", "mp4", "webm", "wmv":
            return .video

        // 压缩文件
        case "7z", "bz2", "gz", "rar", "tar", "zip":
            return .zip

        // HTML文件
        case "htm", "html":
            return .html

        // Markdown文件
        case "markdown", "md", "txt":
            return .markdown

        // EPUB电子书
        case "epub":
            return .epub

        // 文本文件
        case "rtf":
            return .unknown

        // 其他未知类型
        default:
            return .unknown
        }
    }

    /// 获取SwiftUI Color对象
    var swiftUIColor: Color {
        switch self {
        case .image:
            return .blue
        case .pdf:
            return .red
        case .word:
            return .blue
        case .csv, .excel:
            return .green
        case .ppt:
            return .orange
        case .video:
            return .purple
        case .audio:
            return .pink
        case .zip:
            return .gray
        case .html, .url:
            return .cyan
        case .markdown:
            return .indigo
        case .epub:
            return .brown
        case .youtube:
            return .red
        case .unknown:
            return .gray
        }
    }

    /// 获取填充版本的图标名称（适用于文件上传界面）
    var filledIconName: String {
        switch self {
        case .image:
            return "photo.fill"
        case .pdf:
            return "doc.richtext.fill"
        case .word:
            return "doc.text.fill"
        case .csv, .excel:
            return "tablecells.fill"
        case .ppt:
            return "rectangle.on.rectangle.fill"
        case .video:
            return "video.fill"
        case .audio:
            return "music.note"
        case .zip:
            return "archivebox.fill"
        case .html:
            return "globe"
        case .markdown:
            return "text.alignleft"
        case .epub:
            return "book.fill"
        case .url:
            return "link"
        case .youtube:
            return "play.rectangle.fill"
        case .unknown:
            return "doc.fill"
        }
    }

    /// 获取文件类型的中文显示名称
    var displayName: String {
        switch self {
        case .csv:
            return "CSV表格"
        case .epub:
            return "电子书"
        case .html:
            return "网页文件"
        case .image:
            return "图片"
        case .markdown:
            return "Markdown文档"
        case .excel:
            return "Excel表格"
        case .pdf:
            return "PDF文档"
        case .word:
            return "Word文档"
        case .ppt:
            return "PPT演示文稿"
        case .audio:
            return "音频文件"
        case .video:
            return "视频文件"
        case .zip:
            return "压缩文件"
        case .url:
            return "网页链接"
        case .youtube:
            return "YouTube视频"
        case .unknown:
            return "未知类型"
        }
    }
}
