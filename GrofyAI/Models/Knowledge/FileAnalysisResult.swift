import Foundation

// MARK: - 文件分析结果数据模型

/// 文件分析结果，包含片段、摘要、大纲和要点四个部分
struct FileAnalysisResult: Codable, Hashable {
    let snippet: String?
    let summary: String?
    let outline: String?
    let keyPoints: String?
    let tags: [String]?
    let language: String?

    private enum CodingKeys: String, CodingKey {
        case snippet
        case summary
        case outline
        case keyPoints = "key_points"
        case tags
        case language
    }

    init(
        snippet: String? = nil,
        summary: String? = nil,
        outline: String? = nil,
        keyPoints: String? = nil,
        tags: [String]? = nil,
        language: String? = nil
    ) {
        self.snippet = snippet
        self.summary = summary
        self.outline = outline
        self.keyPoints = keyPoints
        self.tags = tags
        self.language = language
    }

    /// 检查是否有任何核心分析内容
    /// 只检查前4个核心字段：snippet, summary, outline, keyPoints
    var hasContent: Bool {
        return hasValidContent(snippet) ||
            hasValidContent(summary) ||
            hasValidContent(outline) ||
            hasValidContent(keyPoints)
    }

    /// 检查是否完整（所有四个核心字段都有内容）
    /// 不包括 tags 和 language 字段的检查
    var isComplete: Bool {
        return hasValidContent(snippet) &&
            hasValidContent(summary) &&
            hasValidContent(outline) &&
            hasValidContent(keyPoints)
    }

    /// 获取有内容的核心字段数量
    var contentFieldCount: Int {
        var count = 0
        if hasValidContent(snippet) { count += 1 }
        if hasValidContent(summary) { count += 1 }
        if hasValidContent(outline) { count += 1 }
        if hasValidContent(keyPoints) { count += 1 }
        return count
    }

    /// 检查字符串是否有有效内容（非空且非纯空白）
    private func hasValidContent(_ content: String?) -> Bool {
        guard let content else { return false }
        return !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    /// 检查 tags 数组是否有有效内容
    var hasValidTags: Bool {
        guard let tags else { return false }
        return tags.contains { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }

    /// 获取有效的 tags（过滤掉空字符串）
    var validTags: [String] {
        return tags?.compactMap { tag in
            let trimmed = tag.trimmingCharacters(in: .whitespacesAndNewlines)
            return trimmed.isEmpty ? nil : trimmed
        } ?? []
    }
}

// MARK: - 文件分析标签页类型

/// 文件分析的标签页类型
enum FileAnalysisTab: String, CaseIterable, Identifiable, Hashable {
    case snippet = "snippet"
    case summary = "summary"
    case outline = "outline"
    case keyPoints = "key_points"

    var id: String { rawValue }

    /// 本地化显示名称
    var displayName: String {
        switch self {
        case .snippet:
            return NSLocalizedString(
                "file_analysis.tab.snippet.title",
                value: "片段",
                comment: "文件分析标签页：代码片段或文档摘录"
            )
        case .summary:
            return NSLocalizedString(
                "file_analysis.tab.summary.title",
                value: "摘要",
                comment: "文件分析标签页：内容摘要"
            )
        case .outline:
            return NSLocalizedString(
                "file_analysis.tab.outline.title",
                value: "大纲",
                comment: "文件分析标签页：文档结构大纲"
            )
        case .keyPoints:
            return NSLocalizedString(
                "file_analysis.tab.key_points.title",
                value: "要点",
                comment: "文件分析标签页：关键要点"
            )
        }
    }

    func getContent(from result: FileAnalysisResult) -> String? {
        switch self {
        case .snippet:
            return result.snippet
        case .summary:
            return result.summary
        case .outline:
            return result.outline
        case .keyPoints:
            return result.keyPoints
        }
    }
}
