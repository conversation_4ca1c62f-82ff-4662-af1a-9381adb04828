import Kingfisher
import SwiftUI

// MARK: - AI消息图片附件展示组件

struct AIImageAttachmentsView: View {
    let images: [MessageImage]
    @State private var showFullScreenViewer = false

    var body: some View {
        if let firstImage = images.first {
            VStack(alignment: .leading, spacing: 0) {
                singleImageView(image: firstImage)
            }
            .fullScreenCover(isPresented: $showFullScreenViewer) {
                FullScreenImageViewer(
                    images: images,
                    initialIndex: 0,
                    isPresented: $showFullScreenViewer
                )
            }
        }
    }

    @ViewBuilder
    private func singleImageView(image: MessageImage) -> some View {
        ImageThumbnailView(
            image: image,
            size: 200,
            cornerRadius: DesignSystem.Rounded.md
        ) {
            showFullScreenViewer = true
        }
    }
}

// MARK: - 图片缩略图组件

struct ImageThumbnailView: View {
    let image: MessageImage
    let size: CGFloat
    let cornerRadius: CGFloat
    let onTap: () -> Void

    @State private var loadingFailed = false

    var body: some View {
        Group {
            if loadingFailed {
                errorStateView
            } else {
                imageView
            }
        }
        .frame(width: size, height: size)
        .cornerRadius(cornerRadius)
        .onTapGesture(perform: onTap)
    }

    @ViewBuilder
    private var imageView: some View {
        if image.fileUrl.isEmpty {
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(DesignSystem.Colors.backgroundCard)
                .overlay {
                    VStack(spacing: DesignSystem.Spacing.xs) {
                        ProgressView()
                            .tint(DesignSystem.Colors.primary)
                            .scaleEffect(0.8)

                        Text("生成中...")
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
        } else if image.fileUrl.hasPrefix("data:image/") {
            ImagePreviewView(imageInfo: image)
                .frame(width: size, height: size)
                .clipped()
        } else if let kfImage = KFImage.from(urlString: image.fileUrl) {
            kfImage
                .placeholder {
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .fill(DesignSystem.Colors.backgroundCard)
                        .overlay {
                            VStack(spacing: DesignSystem.Spacing.xs) {
                                ProgressView()
                                    .tint(DesignSystem.Colors.primary)
                                    .scaleEffect(0.8)

                                Text("加载中...")
                                    .font(DesignSystem.Typography.caption)
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                }
                .onFailure { _ in
                    loadingFailed = true
                }
                .retry(maxCount: 3)
                .fade(duration: 0.25)
                .resizable()
                .aspectRatio(contentMode: .fill)
                .clipped()
        } else {
            errorStateView
        }
    }

    @ViewBuilder
    private var errorStateView: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(DesignSystem.Colors.backgroundCard)
            .overlay {
                VStack(spacing: DesignSystem.Spacing.xs) {
                    Image(systemName: "photo.fill")
                        .font(.title2)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text("加载失败")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Button("重试") {
                        loadingFailed = false
                    }
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.primary)
                }
            }
    }
}

// MARK: - 全屏图片查看器

struct FullScreenImageViewer: View {
    let images: [MessageImage]
    let initialIndex: Int
    @Binding var isPresented: Bool

    @State private var currentIndex = 0

    var body: some View {
        VStack {
            topControlBar

            if images.count > 1 {
                TabView(selection: $currentIndex) {
                    ForEach(images.indices, id: \.self) { index in
                        singleImageView(for: images[index])
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            } else {
                if currentIndex < images.count {
                    singleImageView(for: images[currentIndex])
                } else {
                    Text("图片索引错误: \(currentIndex)/\(images.count)")
                        .foregroundColor(.white)
                }
            }

            bottomInfoView
        }
        .background(Color.black.ignoresSafeArea())
        .onAppear {
            currentIndex = initialIndex
        }
        .statusBarHidden()
    }

    // MARK: - 子视图组件

    @ViewBuilder
    private var topControlBar: some View {
        HStack {
            Spacer()

            HStack {
                Button("下载") {
                    downloadCurrentImage()
                }

                Button("关闭") {
                    isPresented = false
                }
            }
            .foregroundColor(.white)
            .padding()
        }
    }

    @ViewBuilder
    private func singleImageView(for image: MessageImage) -> some View {
        if image.fileUrl.hasPrefix("data:image/") {
            // 处理data URL格式的预览图片
            ImagePreviewView(imageInfo: image)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else if let kfImage = KFImage.from(urlString: image.fileUrl) {
            kfImage
                .placeholder {
                    VStack {
                        ProgressView()
                            .tint(.white)
                        Text("加载中...")
                            .foregroundColor(.white)
                    }
                }
                .onFailure { _ in
                    // Handle failure if needed
                }
                .retry(maxCount: 3)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            // Invalid URL
            VStack {
                Image(systemName: "photo.fill")
                    .font(.largeTitle)
                    .foregroundColor(.white.opacity(0.5))
                Text("无效的图片URL")
                    .foregroundColor(.white)
            }
        }
    }

    @ViewBuilder
    private var bottomInfoView: some View {
        VStack {
            Text("图片 \(currentIndex + 1) / \(images.count)")
                .foregroundColor(.white)

            if currentIndex < images.count {
                Text(images[currentIndex].fileName)
                    .foregroundColor(.white)
                    .font(.caption)
            }

            if images.count > 1 {
                Text("← 滑动查看更多图片 →")
                    .foregroundColor(.white.opacity(0.7))
                    .font(.caption2)
            }
        }
        .padding()
    }

    // MARK: - 下载功能

    private func downloadCurrentImage() {
        guard currentIndex < images.count else { return }
        let currentImage = images[currentIndex]

        if currentImage.fileUrl.hasPrefix("data:image/"), currentImage.fileUrl.contains(";base64,") {
            let components = currentImage.fileUrl.split(separator: ",", maxSplits: 1)
            guard components.count == 2,
                  let imageData = Data(base64Encoded: String(components[1])),
                  let uiImage = UIImage(data: imageData)
            else {
                ToastManager.shared.showError("无法解析图片数据")
                return
            }

            UIImageWriteToSavedPhotosAlbum(uiImage, nil, nil, nil)
            ToastManager.shared.showSuccess("图片已保存到相册")
        } else {
            guard let url = URL(string: currentImage.fileUrl) else {
                ToastManager.shared.showError("图片URL无效")
                return
            }

            KingfisherManager.shared.retrieveImage(with: url) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let value):
                        UIImageWriteToSavedPhotosAlbum(value.image, nil, nil, nil)
                        ToastManager.shared.showSuccess("图片已保存到相册")

                    case .failure:
                        ToastManager.shared.showError("图片保存失败")
                    }
                }
            }
        }
    }
}
