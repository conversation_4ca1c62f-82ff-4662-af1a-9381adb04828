import SwiftUI

// MARK: - 开场白组件

struct Prologue: View {
    let text: String?

    // @State private var displayText = ""
    private let finalTitle = NSLocalizedString("Prompt", comment: "对话提示")

    var body: some View {
        if let text {
            ZStack(alignment: .topLeading) {
                Text(text)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .foregroundColor(DesignSystem.Colors.whiteBlack)
                    .font(.system(size: 14, weight: .regular))
                    .padding(.top, 24)
                    .padding(.bottom, 16)
                    .padding(.horizontal, 16)
                    .background(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.primary.opacity(0.9),
                                DesignSystem.Colors.gradient.opacity(0.95),
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .cornerRadius(DesignSystem.Rounded.md)
                    .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)

                Text(finalTitle)
                    .foregroundColor(DesignSystem.Colors.whiteBlack)
                    .font(.system(size: 12, weight: .medium))
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        LinearGradient(
                            colors: [
                                DesignSystem.Colors.primary,
                                DesignSystem.Colors.gradient,
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(DesignSystem.Rounded.md, corners: [.bottomRight, .topLeft])
            }
            // .onAppear {
            //     typeWriter(text: text)
            // }
        }
    }

    // /**
    //  * @description: 打字机效果
    //  * 短文本（50字以下）：以0.02秒/字的速度显示
    //  * 长文本（100字以上）：以0.01秒/字的速度显示
    //  * 中等长度（50-100字）：速度会在0.02-0.01秒之间线性变化
    //  */
    // private func typeWriter(text: String, at position: Int = 0) {
    //     guard position < text.count else { return }
    //
    //     // 设置基准显示总时长为1秒
    //     let targetDuration = 1.0
    //     // 根据文本长度计算每个字符的显示间隔
    //     var interval = targetDuration / Double(text.count)
    //     // 限制间隔在0.01-0.05秒之间
    //     interval = min(max(interval, 0.01), 0.02)
    //
    //     DispatchQueue.main.asyncAfter(deadline: .now() + interval) {
    //         displayText = String(text.prefix(position + 1))
    //         typeWriter(text: text, at: position + 1)
    //     }
    // }
}
