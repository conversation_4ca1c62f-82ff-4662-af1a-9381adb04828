import Kingfisher
import <PERSON>jickPopups
import SwiftUI

// MARK: - LLM模型选择弹窗

struct LLMModelSelectionPopup: BottomPopup, View {
    @ObservedObject private var modelManager = ModelManager.shared
    let onModelSelected: ((LLMRes) -> Void)?

    init(onModelSelected: ((LLMRes) -> Void)? = nil) {
        self.onModelSelected = onModelSelected
    }

    var body: some View {
        createContent()
    }

    func createContent() -> some View {
        let screenHeight = UIScreen.main.bounds.height
        let popupHeight = screenHeight * 0.65
        let headerHeight: CGFloat = 80
        let contentHeight = popupHeight - headerHeight

        return VStack(spacing: 0) {
            popupHeader

            Group {
                if modelManager.isLoading {
                    loadingView
                } else if modelManager.availableModels.isEmpty {
                    emptyView
                } else {
                    modelListView
                }
            }
            .frame(height: contentHeight)
        }
        .frame(maxWidth: .infinity)
        .frame(height: popupHeight)
        .background(DesignSystem.Colors.backgroundCard)
        .clipShape(RoundedRectangle(cornerRadius: DesignSystem.Rounded.lg))
    }
}

// MARK: - 子视图组件

extension LLMModelSelectionPopup {
    private var popupHeader: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            HStack {
                Text("模型列表")
                    .font(DesignSystem.Typography.titleSmall)
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                Button(action: {
                    Task {
                        await dismissLastPopup()
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.xl)

            Rectangle()
                .fill(DesignSystem.Colors.separator)
                .frame(height: DesignSystem.BorderWidth.thin)
                .padding(.horizontal, DesignSystem.Spacing.xl)
        }
        .background(DesignSystem.Colors.backgroundCard)
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("正在加载模型列表...")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyView: some View {
        EmptyStateView(
            iconName: "exclamationmark.triangle",
            title: "暂无可用模型",
            description: "请检查网络连接或稍后重试",
            iconColor: .orange,
            actionButton: ActionButtonConfig(title: "重新加载") {
                Task {
                    await modelManager.loadModels(force: true)
                }
            },
            style: .standard
        )
    }

    private var modelListView: some View {
        ScrollView(.vertical, showsIndicators: true) {
            LazyVStack(spacing: DesignSystem.Spacing.lg) {
                ForEach(groupedModels, id: \.level) { group in
                    ModelGroupView(
                        level: group.level,
                        models: group.models,
                        selectedModel: modelManager.selectedModel,
                        onModelSelected: { model in
                            modelManager.selectModel(model)
                            onModelSelected?(model)
                            Task {
                                await dismissLastPopup()
                            }
                        }
                    )
                }
            }
            .padding(.vertical, DesignSystem.Spacing.md)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    /// 按级别分组的模型数据
    private var groupedModels: [(level: String, models: [LLMRes])] {
        let grouped = Dictionary(grouping: modelManager.availableModels) { model in
            model.level?.displayName ?? "更多"
        }

        // 按指定顺序排序分组
        let order = ["专家", "高级", "基础", "更多"]
        return order.compactMap { levelName in
            if let models = grouped[levelName], !models.isEmpty {
                return (level: levelName, models: models)
            }
            return nil
        }
    }
}

// MARK: - 模型分组视图

private struct ModelGroupView: View {
    let level: String
    let models: [LLMRes]
    let selectedModel: LLMRes?
    let onModelSelected: (LLMRes) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
            HStack {
                Text(level)
                    .font(DesignSystem.Typography.navigationTitle)
                Spacer()
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)

            VStack(spacing: DesignSystem.Spacing.md) {
                ForEach(models, id: \.id) { model in
                    ModelCardView(
                        model: model,
                        isSelected: selectedModel?.id == model.id,
                        onTap: {
                            onModelSelected(model)
                        }
                    )
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                }
            }
        }
    }
}

// MARK: - 模型卡片视图

private struct ModelCardView: View {
    let model: LLMRes
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                ModelIconView(iconURL: model.icon, provider: model.provider, size: 30)

                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    HStack(alignment: .firstTextBaseline, spacing: DesignSystem.Spacing.xs) {
                        HStack(spacing: DesignSystem.Spacing.xs) {
                            Text(model.safeName)
                                .font(.system(size: DesignSystem.FontSize.lg, weight: .medium))
                                .foregroundColor(isSelected ? .white : DesignSystem.Colors.textPrimary)
                                .lineLimit(1)

                            HStack(spacing: DesignSystem.Spacing.xs) {
                                ForEach(getCapabilityIcons(for: model), id: \.self) { iconName in
                                    Image(systemName: iconName)
                                        .font(.system(size: 12))
                                        .foregroundColor(isSelected ? Color.white.opacity(0.8) : DesignSystem.Colors
                                            .textSecondary
                                        )
                                }
                            }
                        }

                        Spacer()

                        HStack(spacing: DesignSystem.Spacing.xs) {
                            Text(getQuotaText(for: model))
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(isSelected ? Color.white.opacity(0.8) : DesignSystem.Colors
                                    .textSecondary
                                )

                            if let level = model.level {
                                Text(level.displayName)
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background({
                                        switch level {
                                        case .basic:
                                            return Color.green
                                        case .advanced:
                                            return Color.orange
                                        case .expert:
                                            return Color.red
                                        }
                                    }())
                                    .cornerRadius(4)
                            }
                        }
                    }

                    HStack(alignment: .firstTextBaseline) {
                        Text(model.safeSummary)
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .lineLimit(2)

                        Spacer()

                        Text(model.releaseDate ?? "未知日期")
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textTertiary)
                            .frame(minWidth: 60, alignment: .trailing)
                    }
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.backgroundInput
            )
            .cornerRadius(DesignSystem.Rounded.md)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }

    // MARK: - 辅助方法

    /// 根据模型获取功能性图标
    private func getCapabilityIcons(for model: LLMRes) -> [String] {
        var icons: [String] = []

        // 基于模型名称和提供商判断功能
        let modelName = model.safeName.lowercased()
        let provider = model.safeProvider.lowercased()

        // 联网搜索能力判断
        if modelName.contains("gpt") || modelName.contains("claude") || provider.contains("openai") || provider
            .contains("anthropic")
        {
            icons.append("globe")
        }

        // 深度思考能力判断
        if modelName.contains("o1") || modelName.contains("deepseek") || modelName.contains("reasoning") || model
            .level == .expert
        {
            icons.append("brain")
        }

        // 多模态能力判断
        if let inputFormat = model.inputFormat {
            if inputFormat.image {
                icons.append("photo")
            }
            if inputFormat.function {
                icons.append("function")
            }
        }

        return icons
    }

    /// 获取配额文本（占位数据）
    private func getQuotaText(for model: LLMRes) -> String {
        switch model.safeLevel {
        case .basic:
            return "Cost 1 💎"
        case .advanced:
            return "Cost 5 💎"
        case .expert:
            return "Cost 10 💎"
        }
    }
}
