import SwiftUI

// MARK: - 统一空状态视图组件

struct EmptyStateView: View {
    let iconName: String

    let title: String?

    let description: String?

    let iconColor: Color?

    let actionButton: ActionButtonConfig?

    let style: EmptyStateStyle

    init(
        iconName: String,
        title: String? = nil,
        description: String? = nil,
        iconColor: Color? = nil,
        actionButton: ActionButtonConfig? = nil,
        style: EmptyStateStyle = .standard
    ) {
        self.iconName = iconName
        self.title = title
        self.description = description
        self.iconColor = iconColor
        self.actionButton = actionButton
        self.style = style
    }

    var body: some View {
        VStack(spacing: style.contentSpacing) {
            if style.shouldAddTopSpacer {
                Spacer()
            }

            iconView

            textContentView

            if let actionButton {
                actionButtonView(actionButton)
            }

            if style.shouldAddBottomSpacer {
                Spacer()
            }
        }
        .frame(maxWidth: .infinity)
        .frame(minHeight: style.minHeight)
        .padding(.horizontal, style.horizontalPadding)
        .padding(.vertical, style.verticalPadding)
    }

    /// 图标视图
    @ViewBuilder
    private var iconView: some View {
        Image(systemName: iconName)
            .font(.system(size: style.iconSize, weight: .light))
            .foregroundColor(iconColor ?? DesignSystem.Colors.textTertiary)
    }

    /// 文字内容视图
    @ViewBuilder
    private var textContentView: some View {
        VStack(spacing: style.textSpacing) {
            if let title {
                Text(title)
                    .font(style.titleFont)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .multilineTextAlignment(.center)
            }

            if let description {
                Text(description)
                    .font(style.descriptionFont)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }

    /// 操作按钮视图
    @ViewBuilder
    private func actionButtonView(_ config: ActionButtonConfig) -> some View {
        Button(action: config.action) {
            Text(config.title)
                .font(DesignSystem.Typography.cardTitle)
                .foregroundColor(config.style.textColor)
                .padding(.horizontal, DesignSystem.Spacing.xl)
                .padding(.vertical, DesignSystem.Spacing.md)
                .frame(minWidth: 120)
                .background(config.style.backgroundColor)
                .cornerRadius(DesignSystem.Rounded.md)
        }
        .padding(.top, DesignSystem.Spacing.sm)
    }
}

struct ActionButtonConfig {
    let title: String
    let action: () -> Void
    let style: ActionButtonStyle

    init(title: String, style: ActionButtonStyle = .primary, action: @escaping () -> Void) {
        self.title = title
        self.style = style
        self.action = action
    }
}

enum ActionButtonStyle {
    case primary
    case secondary

    var textColor: Color {
        switch self {
        case .primary:
            return .white
        case .secondary:
            return DesignSystem.Colors.primary
        }
    }

    var backgroundColor: Color {
        switch self {
        case .primary:
            return DesignSystem.Colors.primary
        case .secondary:
            return DesignSystem.Colors.primary.opacity(0.1)
        }
    }
}

/// 空状态布局样式
enum EmptyStateStyle {
    /// 标准样式 - 适用于大部分场景
    case standard
    /// 紧凑样式 - 适用于卡片内部或空间受限的场景
    case compact
    /// 全屏样式 - 适用于整个页面的空状态
    case fullScreen

    var iconSize: CGFloat {
        switch self {
        case .standard:
            return 48
        case .compact:
            return 24
        case .fullScreen:
            return 64
        }
    }

    var contentSpacing: CGFloat {
        switch self {
        case .standard:
            return DesignSystem.Spacing.lg
        case .compact:
            return DesignSystem.Spacing.sm
        case .fullScreen:
            return DesignSystem.Spacing.xl
        }
    }

    var horizontalPadding: CGFloat {
        switch self {
        case .standard:
            return DesignSystem.Spacing.lg
        case .compact:
            return DesignSystem.Spacing.md
        case .fullScreen:
            return DesignSystem.Spacing.xl
        }
    }

    var verticalPadding: CGFloat {
        switch self {
        case .standard:
            return DesignSystem.Spacing.lg
        case .compact:
            return DesignSystem.Spacing.md
        case .fullScreen:
            return DesignSystem.Spacing.xxl
        }
    }

    var minHeight: CGFloat? {
        switch self {
        case .standard:
            return nil
        case .compact:
            return 80
        case .fullScreen:
            return nil
        }
    }

    var shouldAddTopSpacer: Bool {
        switch self {
        case .fullScreen, .standard:
            return true
        case .compact:
            return false
        }
    }

    var shouldAddBottomSpacer: Bool {
        switch self {
        case .fullScreen, .standard:
            return true
        case .compact:
            return false
        }
    }

    var titleFont: Font {
        switch self {
        case .fullScreen, .standard:
            return DesignSystem.Typography.headline
        case .compact:
            return DesignSystem.Typography.subheadline
        }
    }

    var descriptionFont: Font {
        switch self {
        case .fullScreen:
            return DesignSystem.Typography.subheadline
        case .standard:
            return DesignSystem.Typography.caption
        case .compact:
            return DesignSystem.Typography.caption
        }
    }

    var textSpacing: CGFloat {
        switch self {
        case .fullScreen, .standard:
            return DesignSystem.Spacing.sm
        case .compact:
            return DesignSystem.Spacing.xs
        }
    }
}
