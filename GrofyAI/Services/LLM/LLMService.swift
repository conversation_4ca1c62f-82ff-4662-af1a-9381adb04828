import Foundation

final class LLMService {
    func getModelList() async throws -> [LLMRes] {
        do {
            let headers = [
                "Content-Type": "application/json",
            ]

            let models: [LLMRes] = try await Http.shared.GET(
                LLMApi.modelList.path,
                params: ["tenantId": AppConfig.Auth.tenantId],
                headers: headers
            )

            return models

        } catch let error as BusinessError {
            print("LLMService: API业务错误 - Code: \(error.code), Message: \(error.message)")

            if error.code == 401 {
                print("LLMService: 认证失败，触发重新登录流程")
                AuthStore.shared.clearUser()

                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .authenticationRequired, object: nil)
                }

                throw NetworkError.authorizationError
            }

            throw error

        } catch let error as NetworkError {
            print("LLMService: 网络错误 - \(error.localizedDescription)")

            switch error {
            case .noConnection:
                print("LLMService: 网络连接不可用")
            case .noData:
                print("LLMService: 服务器返回空数据")
            case .timeout:
                print("LLMService: 请求超时")
            case .decodingError(let decodingError):
                print("LLMService: 数据解码错误 - \(decodingError.localizedDescription)")
            case .authorizationError:
                print("LLMService: 授权错误，需要重新登录")
                throw error
            default:
                print("LLMService: 其他网络错误")
            }

            throw error

        } catch {
            print("LLMService: 未知错误 - \(error.localizedDescription)")
            throw error
        }
    }

    // 获取模型积分
    func creditsItemsData() async throws -> [CreditsItem] {
        let res: [CreditsItem] = try await Http.shared.GET(LLMApi.creditsItems.path)
        return res
    }

    // 获取积分消耗列表
    func creditsConsumeList(req: CreditsConsumeReq) async throws -> [CreditsConsumeRes] {
        let res: [CreditsConsumeRes] = try await Http.shared.GETPAGE(
            LLMApi.creditsConsume.path,
            params: req.toDictionary()
        )
        return res
    }
}
