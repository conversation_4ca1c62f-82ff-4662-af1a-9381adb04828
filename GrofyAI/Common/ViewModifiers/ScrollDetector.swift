import SwiftUI

/// 滚动状态检测修饰器
struct ScrollDetector: ViewModifier {
    @Binding var isAtBottom: Bool
    @Binding var isUserScrolling: Bool

    @State private var lastOffset: CGFloat = 0
    @State private var scrollTimer: Timer?

    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .preference(
                            key: ScrollOffsetPreferenceKey.self,
                            value: geometry.frame(in: .named("scroll")).minY
                        )
                }
            )
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
                detectScrolling(offset: offset)
            }
    }

    private func detectScrolling(offset: CGFloat) {
        if abs(offset - lastOffset) > 5 {
            isUserScrolling = true

            scrollTimer?.invalidate()
            scrollTimer = nil

            scrollTimer = Timer.scheduledTimer(withTimeInterval: 0.2, repeats: false) { _ in
                DispatchQueue.main.async {
                    isUserScrolling = false
                    scrollTimer = nil
                }
            }
        }

        lastOffset = offset
    }
}

/// 底部检测视图
struct BottomDetector: View {
    @Binding var isAtBottom: Bool

    private static let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        GeometryReader { geometry in
            Color.clear
                .onAppear {
                    isAtBottom = true
                }
                .onDisappear {
                    isAtBottom = false
                }
                .onChange(of: geometry.frame(in: .global)) { newFrame in
                    isAtBottom = newFrame.maxY <= Self.screenHeight
                }
        }
        .frame(height: 1)
    }
}

extension View {
    /// 添加滚动检测
    func scrollDetector(isAtBottom: Binding<Bool>, isUserScrolling: Binding<Bool>) -> some View {
        modifier(ScrollDetector(isAtBottom: isAtBottom, isUserScrolling: isUserScrolling))
    }
}
