import Foundation

// MARK: - 应用通知定义

extension Notification.Name {
    /// 需要重新认证的通知
    static let authenticationRequired = Notification.Name("AuthenticationRequired")

    /// 用户认证状态就绪通知（包括登录，注册，刷新 token）
    static let userAuthenticationReady = Notification.Name("UserAuthenticationReady")

    /// 登录失败通知
    static let loginFailure = Notification.Name("LoginFailure")

    /// Token过期通知
    static let tokenExpired = Notification.Name("TokenExpired")

    /// 模型列表更新通知
    static let modelListUpdated = Notification.Name("ModelListUpdated")

    /// 请求加载模型列表通知
    static let requestModelListLoad = Notification.Name("RequestModelListLoad")

    /// 知识库分类更新通知
    static let knowledgeCategoryUpdated = Notification.Name("KnowledgeCategoryUpdated")

    /// 文件移动完成通知
    static let knowledgeFileMovedNotification = Notification.Name("knowledgeFileMovedNotification")

    /// 语音识别反馈通知
    static let speechRecognitionFeedback = Notification.Name("speechRecognitionFeedback")

    /// 文件对话切换到历史记录通知
    static let fileChatSwitchToHistory = Notification.Name("fileChatSwitchToHistory")

    /// 图片对话切换到历史记录通知
    static let imageChatSwitchToHistory = Notification.Name("imageChatSwitchToHistory")
}
